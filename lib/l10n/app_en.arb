{"appMotto": "Your business's diary", "@appMotto": {"description": "The application motto"}, "yes": "Yes", "no": "No", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "days": "days", "lastWeek": "Last week", "yesterday": "Yesterday", "anHourAgo": "An hour ago", "aMinuteAgo": "A minute ago", "justNow": "Just now", "anHourLeft": "An 1h left", "aMinuteLeft": "A 1m ago", "email": "Email", "emails": "Emails", "password": "Password", "login": "<PERSON><PERSON>", "logout": "Logout", "signup": "Signup", "reset": "Reset", "back": "Back", "cancel": "Cancel", "grant": "<PERSON>", "edit": "Edit", "finish": "Finish", "update": "Update", "save": "Save", "delete": "Delete", "type": "Type", "date": "Date", "birthday": "Birthday", "status": "Status", "progress": "Progress", "completed": "Completed", "failed": "Failed", "download": "Download", "open": "Open", "openWith": "Open with", "tag": "Tag", "tags": "Tags", "sync": "Sync", "syncing": "Syncing", "welcome": "Welcome", "address": "Address", "addresses": "Addresses", "latitude": "Latitude", "longitude": "Longitude", "send": "Send", "sending": "Sending", "sent": "<PERSON><PERSON>", "share": "Share", "sharing": "Sharing", "staff": "Staff", "simple": "Simple", "switch_": "Switch", "invite": "Invite", "inProgress": "In Progress...", "resend": "Resend", "deactivate": "Deactivate", "audioRecording": "Audio Recording", "voiceRecognition": "Voice Recognition", "speechNote": "Speech Note", "trackerEntry": "Tracked Entry", "other": "Other", "otherSingular": "Other", "feed": "Feed", "posts": "Posts", "resources": "Resources", "readMore": "Read more", "report": "Report", "reports": "Reports", "spreadsheet": "Spreadsheet", "creating": "Creating", "processing": "Processing", "opening": "Opening", "today": "Today", "elapsed": "Elapsed", "start": "Start", "stop": "Stop", "notifications": "Notifications", "title": "Title", "subtitle": "Subtitle", "text": "Text", "view": "View", "all": "All", "allWithTemplates": "All (with Temp<PERSON>)", "clear": "Clear", "clearAll": "Clear All", "assignee": "Assignee", "signee": "Signee", "creator": "Creator", "client": "Client", "include": "Include", "customize": "Customize", "template": "Template", "retry": "Retry", "retrieve": "Retrieve", "project": "Project", "company": "Company", "area": "Area", "approved": "Approved", "rejected": "Rejected", "received": "Received", "new_": "New", "create": "Create", "missing": "Missing", "empty": "Empty", "tryAgain": "Try Again", "longPress": "Long Press", "discard": "Discard", "entries": "Entries", "home": "Home", "office": "Office", "work": "Work", "mobile": "Mobile", "main": "Main", "oneHour": "1 hour", "nHours": "{n} hours", "group": "Group", "ungroup": "Ungroup", "@nHours": {"description": "To indicate number of hours", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "daysAgo": "{n} days ago", "@daysAgo": {"description": "To indicate the report creation date in days.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "hoursAgo": "{n} hours ago", "@hoursAgo": {"description": "To indicate the report creation date in hours.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "minutesAgo": "{n} minutes ago", "@minutesAgo": {"description": "To indicate the report creation date in minutes.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "secondsAgo": "{n} seconds ago", "@secondsAgo": {"description": "To indicate the report creation date in seconds.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "hoursLeft": "{n}h left", "@hoursLeft": {"description": "To indicate the user location tracking left time in hours.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "minutesLeft": "{n}m left", "@minutesLeft": {"description": "To indicate user location tracking left time in minutes.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "secondsLeft": "{n}s left", "@secondsLeft": {"description": "To indicate user location tracking left time in  seconds.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "willStopSoon": "Will stop soon", "@willStopSoon": {"description": "To indicate user location tracking will stop soon"}, "permissionRequired": "Permission Required", "@permissionRequired": {"description": "Location Tracking consent dialog title"}, "trackingConsent": "Bitacora.io collects location data to enable route tracking initiated by the user even when app is closed or not in use.", "@trackingConsent": {"description": "Location Tracking consent dialog text"}, "relatedEntries": "{n} related entries", "@relatedEntries": {"description": "Title of a group's detail page", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "newReport": "New Report", "@newReport": {"description": "Title for the report form page when creating a new report."}, "reportIncludeCaption": "Select which entry fields to include in your report.", "@reportIncludeCaption": {"description": "Include section caption in the report creation form page. Indicates which entry fields will be included in report."}, "reportCustomizeCaption": "Add a header to your report.", "@reportCustomizeCaption": {"description": "Customize section caption in the report creation form page."}, "reportFileCouldNotBeFound": "The report file could not be found.", "@reportFileCouldNotBeFound": {"description": "Toast message when user wants to open a report file that does not exist."}, "noAppToOpen": "No app found to open this file.", "@noAppToOpen": {"description": "No app to open external file toast message"}, "filters": "Filters", "@filters": {"description": "noun: sorting or blocking access to certain data"}, "addFilters": "Add Filters", "@addFilters": {"description": "Daylog filter panel title"}, "noActiveFilters": "No active filters", "@noActiveFilters": {"description": "No filters indicator of daylog appbar"}, "reportFiltersCaption": "Apply filters to view selected entries.", "@reportFiltersCaption": {"description": "Filters section caption in the report creation form page."}, "externalSignee": "-External-", "@externalSignee": {"description": "Used to label an external user (outside of organization). Usually email is specified. i.e. -External- [<EMAIL>]"}, "signatureType": "Signature Type", "@signatureType": {"description": "label for different signature types: i.e. approved, rejected, received"}, "search": "Search", "@search": {"description": "noun: act of searching"}, "searchInput": "Input exact word or phrase for search...", "@searchInput": {"description": "label describing what to type in search fields"}, "noResults": "No results", "@noResults": {"description": "shows when there are no results in a search"}, "format": "Format", "@format": {"description": "noun: the way in which something is arranged or set out."}, "period": "Period", "@period": {"description": "noun: as in time period."}, "nDays": "{n} days", "@nDays": {"description": "Number of days. Shouldn't be used for a single day.", "placeholders": {"n": {"type": "int", "format": "decimalPattern", "example": "7"}}}, "distanceTraveled": "Distance traveled: {n} {unit}", "@distanceTraveled": {"description": "Distance traveled text of location tracking entry.", "placeholders": {"n": {"type": "String", "example": "104.2"}, "unit": {"type": "String", "example": "km"}}}, "averageSpeed": "Average speed: {n} {unit}", "@averageSpeed": {"description": "Average speed text of location tracking entry.", "placeholders": {"n": {"type": "double", "format": "decimalPattern", "example": "15.2"}, "unit": {"type": "String", "example": "m/h"}}}, "location": "Location", "@location": {"description": "GPS (latitude, longitude) location"}, "networkError": "There was an issue with the network.", "@networkError": {"description": "Typically shows in a toast because user has no connection."}, "forgotPassword": "Forgot password?", "@forgotPassword": {"description": "Asks if password was forgotten."}, "resetPassword": "Reset password", "@resetPassword": {"description": "Action to reset the user's password."}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Asks the user if they don't have an account."}, "name": "Name", "@name": {"description": "Used for the user's name."}, "phoneNumber": "Phone number", "@phoneNumber": {"description": "Used for the user's phone number."}, "phoneNumbers": "Phone numbers", "@phoneNumbers": {"description": "Used for the user's phone number section."}, "organizations": "Organizations", "proPlan": "Pro Plan", "@proPlan": {"description": "Used for badge labeling org as Pro."}, "failedUserInvite": "⚠️ Error sending invite", "@failedUserInvite": {"description": "Used for labeling failed user invitation."}, "copyClipboard": "Copied {text}.", "@copyClipboard": {"description": "Toast text to notify that it was copied to the clipboard (keep short).", "placeholders": {"text": {"type": "String", "example": "<EMAIL>"}}}, "activeOrganization": "Active Organization", "@activeOrganization": {"description": "The current/active/selected) organization."}, "pdfOrExcel": "PDF or Excel", "@pdfOrExcel": {"description": "Menu option for exporting a pdf or excel report."}, "createAndShareReport": "Create & Share PDF", "@createAndShareReport": {"description": "Menu option subtitle for exporting a pdf report."}, "reportScreenTitle": "Looking to create reports or export your data?", "@reportScreenTitle": {"description": "Title question for the report screen where the user will be directed on how to export their data and create reports."}, "reportScreenMessage": "Visit the Bitacora.io web panel to manage your information.", "@reportScreenMessage": {"description": "Message prompting the user to visit the website or contact for help regarding exporting information or creating reports."}, "reportTemplateBaseDescription": "Regular / Day", "@reportTemplateBaseDescription": {"description": "Describes a regular report template (intended for daily use)"}, "reportTemplateCompactDescription": "Compact / Hr", "@reportTemplateCompactDescription": {"description": "Describes a compact report template (intended for hourly use)"}, "reportTemplateCompactMinimalDescription": "Compact Minimal", "@reportTemplateCompactMinimalDescription": {"description": "Describes a minimal compact report template"}, "reportTemplateCompactTwoColumnsDescription": "Compact / 2 Columns", "@reportTemplateCompactTwoColumnsDescription": {"description": "Describes a two-columned compact report template"}, "reportFailedToCreate": "Failed to create report.", "@reportFailedToCreate": {"description": "Top Snackbar text used when report creation fails."}, "syncDescription": "Updated {updatedAt}", "@syncDescription": {"description": "Menu subtitle for sync action. Informs the last sync update time.", "placeholders": {"updatedAt": {"type": "String", "example": "Jan 31, 2021"}}}, "loginRequired": "<PERSON><PERSON> Required", "@loginRequired": {"description": "Menu subtitle for sync action. Informs when session is invalid."}, "updateRequired": "Update Required", "@updateRequired": {"description": "Menu subtitle for sync action. Informs when app version is invalid."}, "incompleteSync": "There are logs that have not yet synced. Make sure you have good internet and tap Sync.", "@incompleteSync": {"description": "Menu subtitle to report that synchronization is incomplete."}, "syncInProgress": "Sync in Progress.", "@syncInProgress": {"description": "Report creation warning title message when app has pending uploads."}, "syncReport": "There still appear to be records to be synced in your organization, your report may be incomplete.", "@syncReport": {"description": "Report creation warning body message when app has pending uploads."}, "sessionExpired": "The session has expired.\nEnter your password to sync your data.", "@sessionExpired": {"description": "Shows when the user needs to re enter their password."}, "apiExpired": "This app version is no longer supported.\nUpdate to sync your data.", "@apiExpired": {"description": "Shows when the user needs to update the app."}, "leaveForLater": "Leave for later", "@leaveForLater": {"description": "Recover Session leave for later button text"}, "youWontBeAbleToSyncData": "You won't be able to sync data", "@youWontBeAbleToSyncData": {"description": "Recover Session leave for later button warning text"}, "credentialNotMatchWithCurrentUser": "The credentials don't match with the current user.", "@credentialNotMatchWithCurrentUser": {"description": "Used when an user recover session with wrong credentials."}, "userSettings": "User settings", "@userSettings": {"description": "Settings configurable by and applicable to the user."}, "includeOnEntryCapture": "Include on entry capture", "@includeOnEntryCapture": {"description": "Determines that an option (i.e location) will be included on entry capture."}, "locationTrackingActive": "Location tracking active", "@trackingPathInEntries": {"description": "(Top snack bar/notification) title when app is tracking path in entries."}, "locationTrackingFromAnotherOrg": "The tracking entry belongs to {organizationName}.", "@locationTrackingFromAnotherOrg": {"description": "Toast text when entry location tracking was tapped from another org.", "placeholders": {"organizationName": {"type": "String", "example": "Awesome Company"}}}, "myActiviy": "My Activiy", "@myActiviy": {"description": "App drawer item title to allow view your activity"}, "myActiviyDescription": "View the number of entries created this week and this month", "@myActiviyDescription": {"description": "App drawer item subtitle to allow view your activity"}, "weekChartTitle": "This Week", "@weekChartTitle": {"description": "Week chart title"}, "weekChartSubtitle": "Compared to Last Week", "@weekChartSubtitle": {"description": "Week chart subtitle"}, "monthChartTitle": "This Month", "@monthChartTitle": {"description": "Month chart title"}, "monthChartSubtitle": "entries compared to {count} Last Month", "@monthChartSubtitle": {"description": "Month chart subtitle", "placeholders": {"count": {"type": "int", "example": "73"}}}, "allowTrackingUser": "Track My Location", "@allowTrackingUser": {"description": "App drawer item title to allow user tracking location."}, "forThisOrganization": "For this organization", "@forThisOrganization": {"description": "App drawer item subtitle to allow start user tracking location."}, "forOrganization": "For {organizationName}", "@forOrganization": {"description": "App drawer item subtitle to allow stop user tracking location.", "placeholders": {"organizationName": {"type": "String", "example": "Awesome Company"}}}, "backgroundLocationTracking": "Background location tracking active. Tap to open.", "@activeTracking": {"description": "Notification body text when app background location tracking is active."}, "systemPermissions": "System permissions", "@systemPermissions": {"description": "App related permissions granted through operating system"}, "phoneInputCaption": "For improved support & upcoming WhatsApp integration.", "@phoneInputCaption": {"description": "Signup phone input caption"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Menu title for go to privacy policy."}, "videoTutorials": "Video Tutorials", "@videoTutorials": {"description": "Menu title for go to youtube video tutorials."}, "contactSupport": "Contact Support", "@contactSupport": {"description": "Text button option to contact WhatsApp support."}, "sendUsWhatsApp": "WhatsApp Support", "@sendUsWhatsApp": {"description": "Menu option for send support WhatsApp message."}, "emailSupport": "Email Support", "@emailSupport": {"description": "Menu option for send email contact."}, "supportMessage": "Hi, I'm a Bitacora.io user {email}, I have a question or comment...", "@supportMessage": {"description": "Initial message for init support contact", "placeholders": {"email": {"type": "String", "example": "<EMAIL>"}}}, "loginSupportMessage": "Hi, I have a question or comment...", "@loginSupportMessage": {"description": "Initial message WhatsAap support contact in login page"}, "theme": "Theme", "@theme": {"description": "A user setting category for changing the app's color scheme."}, "system": "System", "@system": {"description": "Referring to the current underlying system (or operating system)"}, "light": "Light", "@light": {"description": "Referring to the light theme or color scheme (light mode)"}, "dark": "Dark", "@dark": {"description": "Referring to the dark theme or color scheme (dark mode)"}, "allProjects": "All: Projects / Categories / Locations", "@allProjects": {"description": "Option to show data related for all projects/categories/locations"}, "menu": "<PERSON><PERSON>", "@menu": {"description": "Generic string for a menu (list of options)."}, "noScheduledTasks": "No scheduled tasks.", "@noScheduledTasks": {"description": "A label shown when there are no scheduled tasks for the user."}, "log": "Log", "@log": {"description": "The basic type of log, related to any kind of activity."}, "inventory": "Inventory", "@inventory": {"description": "A type of log, related to inventory activity."}, "personnel": "Personnel", "@personnel": {"description": "A type of log, related to personnel activity."}, "movement": "Movement", "@movement": {"description": "Describes the movement of inventory."}, "outgoing": "Outgoing", "@outgoing": {"description": "Inventory movement going out."}, "incoming": "Incoming", "@incoming": {"description": "Inventory movement coming in."}, "invalidEmail": "Invalid email", "@invalidEmail": {"description": "Used when an email text field has invalid contents."}, "alreadyUsedEmail": "Email already used in this organization", "@alreadyUsedEmail": {"description": "Used when invite new user with duplicated email to org"}, "invalidPhoneNumber": "Invalid phone number", "@invalidPhoneNumber": {"description": "Used when an phone number text field has invalid contents."}, "emptyName": "Empty name", "@emptyName": {"description": "Used when a name text field has empty contents."}, "emptyPassword": "Empty password", "@emptyPassword": {"description": "Used when a password text field has empty contents."}, "somethingWentWrong": "Something went wrong.", "@somethingWentWrong": {"description": "A generic message for unexpected errors."}, "tryAgainLater": "Try again later.", "@tryAgainLater": {"description": "Generic try again later message."}, "requiredField": "This field is required.", "@requiredField": {"description": "Shown below the missing required field when trying to save."}, "formQuantity": "# / Qtty.", "@formQuantity": {"description": "The quantity field label for a log."}, "formTitle": "Activity / Entry Title", "@formTitle": {"description": "The activity/title field label for a log entry."}, "formProject": "Proj., Category or Location", "@formProject": {"description": "The project field label for a log."}, "formSublocation": "Sub-proj. cat. loc", "@formSublocation": {"description": "The sub-project/category field label for a log."}, "formComments": "Comments / Description", "@formComments": {"description": "The comments/description field label for a log."}, "progressComments": "Progress Comments / Description", "@progressComments": {"description": "The comments/description field label for a progress log."}, "locationDenied": "Location permissions are denied.", "@locationDenied": {"description": "When the user denied geolocation permissions forever."}, "photoDenied": "Photo gallery access is denied.", "@photoDenied": {"description": "When the user denied photo gallery access."}, "videoDenied": "Video gallery access is denied.", "@videoDenied": {"description": "When the user denied video gallery access."}, "fileDenied": "Storage access is denied.", "@fileDenied": {"description": "When the user denied file access."}, "cameraDenied": "Camera access is denied.", "@cameraDenied": {"description": "When the user denied camera access."}, "recordingDenied": "Audio recording permissions are denied.", "@recordingDenied": {"description": "When the user denied audio recording permissions forever."}, "voiceRecognitionError": "⚠️ Error due to speech recognition.", "@voiceRecognitionError": {"description": "Toast text for speech to text error. Keep short."}, "goToAppSystemSettings": "Go to Settings", "@goToAppSystemSettings": {"description": "Go to the application settings on the system."}, "newEntries": "New entries", "@newEntries": {"description": "Shows when a user has input a new field (project, title, etc.)"}, "offered": "Offered", "@offered": {"description": "Labels an entry. The service/work was offered. i.e. someone hired you"}, "itemName": "Item Name", "@itemName": {"description": "The item name field label for an inventory log."}, "from_": "From:", "@from_": {"description": "The label for the source of the inventory log. Keep short."}, "to_": "To:", "@to_": {"description": "The label for the destination of the inventory log. Keep short."}, "recipient": "Recipient", "@recipient": {"description": "The label for the recipient of the inventory log."}, "provider": "Provider", "@provider": {"description": "The label for the provider of the inventory log."}, "unit_": "UNIT", "@unit_": {"description": "The label used when specifying the price per unit."}, "total_": "TOT", "@total_": {"description": "The label used when specifying the total price."}, "expense": "Expense", "@expense": {"description": "The label used for the cost price of the inventory log."}, "income": "Income", "@income": {"description": "The label used for the sale price of the inventory log."}, "reason": "Reason", "@reason": {"description": "The label used for the reason of the inventory log."}, "na": "N/A", "@na": {"description": "Used to mark a payment status as not available."}, "paid": "PAID", "@paid": {"description": "Used to mark a payment status as paid."}, "unpaid": "NOT PAID", "@unpaid": {"description": "Used to mark a payment status as not paid."}, "numHours_": "# OF HOURS", "@numHours_": {"description": "Used to select the number of hours for a personnel log"}, "time_": "Time", "@time_": {"description": "Used to select the clock-in clock-out times for a personnel log. Must be very short."}, "hours": "Hours", "@hours": {"description": "Used as the label for inputting the number of hours for a personnel log"}, "entrance": "Entrance", "@entrance": {"description": "Used as the label for the clock-in time for a personnel log."}, "exit": "Exit", "@exit": {"description": "Used as the label for the clock-out time for a personnel log."}, "scheduleEntry": "Schedule Entry", "@scheduleEntry": {"description": "Title for schedule option in entry form."}, "startDate": "Start Date", "@startDate": {"description": "Used for the start date of a range of dates."}, "endDate": "End Date", "@endDate": {"description": "Used for the end date of a range of dates."}, "startTime": "Start Time", "@startTime": {"description": "Used for the start time of a range of times."}, "endTime": "End Time", "@endTime": {"description": "Used for the end time of a range of times."}, "timer": "Timer", "@timer": {"description": "Default title for entry timer."}, "entryTimer": "Entry Timer", "@entryTimer": {"description": "Default title for top snack bar entry timer."}, "moreThanOneTimer": "You can't have more than one active timer.", "@moreThanOneTimer": {"description": "Toast description to prevent more than one timer"}, "moreThanOneLocationTracking": "You can't have more than one tracking entry.", "@moreThanOneLocationTracking": {"description": "Toast description to prevent more than one tracking entry"}, "progressive": "Progressive", "@progressive": {"description": "A type of scheduled entry. Allows multiple progress reports 0% -> 100%."}, "complete": "Complete", "@complete": {"description": "A type of scheduled entry. Allows only one progress report."}, "myFirstReportIsReady": "Your first PDF report is ready!", "@myFirstReportIsReady": {"description": "Notification message when the first report has been created successfully."}, "scheduledEntry": "Scheduled Entry", "@scheduledEntry": {"description": "Title for scheduled option in entry form. Entry has already been scheduled"}, "incomeOrExpense": "Income and/or expense", "@incomeOrExpense": {"description": "Title for income/expense option in entry form."}, "addAComment": "Add a comment", "@addAComment": {"description": "Invite the user to add a comment to a something"}, "assignedTo": "Assigned to", "@assignedTo": {"description": "EditText hint for the assigned user of an entry."}, "assignedToMe": "Assigned to me", "@assignedToMe": {"description": "Filter of Open State Sliding panel."}, "moreFields": "More Fields", "@moreFields": {"description": "Text of button to change Simplelog to Worklog in entry form page."}, "logoutTitle": "Logout", "@logoutTitle": {"description": "The title of the logout dialog."}, "logoutMessage": "Are you sure that you want to log out?", "@logoutMessage": {"description": "The message of the logout dialog."}, "logoutWillLoseData": "*You have local changes, they will be permanently lost.", "@logoutWillLoseData": {"description": "An extra line on the logout dialog message showing that data will be permanently lost."}, "chatWillBeLost": "The chat will be lost on exit.", "@chatWillBeLost": {"description": "The message of gpt chat page exit dialog"}, "writeSomething": "Write something...", "@writeSomething": {"description": "Placeholder text of chat input field."}, "entryNotSaved": "Entry not saved", "@entryNotSaved": {"description": "The message of entry form page exit dialog"}, "doodleNotSaved": "<PERSON><PERSON> not saved", "@doodleNotSaved": {"description": "The message of doodle canvas mode exit dialog"}, "willLoseData": "How do you want to proceed?", "@willLoseData": {"description": "An extra line on the exit dialog message showing that changes will be permanently lost."}, "chooseYourOrg": "Choose your active organization", "@chooseYourOrg": {"description": "Title for a ui that allows the user to choose their active organization."}, "wereInvitedOrg": "You were invited to a new organization", "@wereInvitedOrg": {"description": "Title of the top snackbar that notifies that the user was invited to a new organization."}, "switchOrganization": "Switch Organization", "@switchOrganization": {"description": "Tooltip text for launching intent to choose organization."}, "reportEntryType": "Entry Type", "@reportEntryType": {"description": "Title for selecting the entry type when generating reports."}, "photoPdf": "Photo PDF", "@photoPdf": {"description": "Specifying a report format of PDF with photos."}, "startDateEndDate": "Start Date / End Date", "@startDateEndDate": {"description": "Option to include date fields in reports."}, "incomeExpense": "Income / Expense", "@incomeExpense": {"description": "Option to include income and expense fields in reports."}, "providerStatus": "Provider / Status", "@providerStatus": {"description": "Option to include provider and status fields in reports."}, "startTomorrow": "Start tomorrow", "@startTomorrow": {"description": "Text in notification to remind the start of scheduled entry"}, "finishToday": "Finish today", "@finishToday": {"description": "Text in notification to remind the finish of scheduled entry"}, "finishTomorrow": "Finish tomorrow", "@finishTomorrow": {"description": "Text in notification to remind the finish of scheduled entry"}, "scheduledEntries": "Scheduled Entries", "@scheduledEntries": {"description": "A notifications setting sub-category Title for scheduled entries."}, "notificationTime": "Notification time", "@notificationTime": {"description": "Notification time for scheduled entries."}, "notifyDayOf": "Notify the day of", "@notifyDayOf": {"description": "A notification setting for enable/disable schedule entry day of notifications."}, "alertOnTheStartAndOrEndDayOfTheTask": "<PERSON><PERSON> on the start and/or end day of the task.", "@alertOnTheStartAndOrEndDayOfTheTask": {"description": "A notification setting subtitle for enable/disable schedule entry same day notifications."}, "notifyTheDayBefore": "Notify the day before", "@notifyTheDayBefore": {"description": "A notification setting for enable/disable schedule entry one day before notifications."}, "alertTheDayBeforeTheTaskStartsAndOrEnds": "Alert the day before the task starts and/or ends.", "@alertTheDayBeforeTheTaskStartsAndOrEnds": {"description": "A notification setting subtitle for enable/disable schedule entry the day before notifications."}, "dailyReminder": "Daily reminder", "@notifyDailyReminder": {"description": "Daily reminder notifications name"}, "dailyReminderDescription": "Notifies daily reminding you to log your notes and evidences", "@dailyReminderDescription": {"description": "Daily reminder notifications description"}, "logYourActivitiesOfToday": "Log your activities of today", "@logYourActivitiesOfToday": {"description": "Daily reminder notification title"}, "dailyReminderBody": "Don’t forget to log today’s notes and evidences so you don’t pile up work.", "@dailyReminderBody": {"description": "Daily reminder notification body"}, "usePreviousVersion": "Use Previous Version", "@usePreviousVersion": {"description": "Prompts user to use the previous (legacy) version of some UI"}, "bugReport": "Bug Report", "@bugReport": {"description": "(noun) Title of notification when users reports a bug."}, "bugReportChannelDescription": "Notifies when your bug reports are ready and allows you to share them.", "@bugReportChannelDescription": {"description": "The description for the notification channel associated with bug report creation."}, "bugReportTapToShare": "Tap to share.", "@bugReportTapToShare": {"description": "Shows below a bug report notification with a CTA for user to tap to share bug report file."}, "bugReportHint": "Please describe your issue.", "@bugReportHint": {"description": "A hint for a textfield where we ask the user to describe their issue for a bug report."}, "deleteAccountTitle": "Delete Account", "@deleteAccountTitle": {"description": "Title for a dialog confirming intention to delete the current account"}, "deleteAccountMessage": "This will initiate the process of deleting your account and log you out.", "@deleteAccountMessage": {"description": "Title for a dialog confirming intention to delete the current account"}, "farFromQr": "You are too far from the QR code location", "@farFromQr": {"description": "Error message of QR scanner when user is too far from the QR location"}, "qrDoesNotBelongOrg": "This QR does not belong to your current active organization", "@qrDoesNotBelongOrg": {"description": "Error message of QR scanner when QR code does not belong to the current active organization"}, "deleteTitle": "Are you sure?", "@deleteTitle": {"description": "The title of the delete dialog."}, "deleteMessage": "This will permanently delete the entry.", "@deleteMessage": {"description": "The message of the delete-entry dialog."}, "deleteSelectionTitle": "Are you sure?", "@deleteSelectionTitle": {"description": "The title of the delete selection dialog."}, "deleteSelectionMessage": "This will permanently delete the entries.", "@deleteSelectionMessage": {"description": "The title of the delete selection dialog."}, "ungroupGroupTitle": "Are you sure?", "@deleteGroupTitle": {"description": "The title of the ungroup dialog."}, "ungroupGroupMessage": "This will permanently ungroup all entries.", "@deleteGroupMessage": {"description": "The title of the ungroup dialog."}, "selectedEntries": "{count, plural, =1{{count} selected entry} other{{count} selected entries}}", "@selectedEntries": {"description": "Text to indicate the number of entries selected", "placeholders": {"count": {"type": "num", "format": "compact"}}}, "selectedNames": "{count, plural, =1{{count} selected name} other{{count} selected names}}", "@selectedItems": {"description": "Text to indicate the number of personnellog names selected", "placeholders": {"count": {"type": "num", "format": "compact"}}}, "deleteReport": "This will permanently delete the selected report(s).", "@deleteReport": {"description": "The message of the delete-report dialog."}, "freePlan": "Free Plan", "@freePlan": {"description": "Text for active plan indicator when active plan is free"}, "freePlanDescription": "Record your activity notes day by day and view it on the web and app.", "@freePlanDescription": {"description": "Description for pro features page when active plan is free"}, "trialPlan": "Trial Plan", "@trial": {"description": "Text for active plan indicator when active plan is free trial"}, "trialPlanDescription": "Capture, create reports and add collaborators to explore on the web and app.", "@trialPlan": {"description": "Description for pro features page when active plan is free trial"}, "discoverBitacora": "Start discovering Bitacora.io:", "@discoverBitacora": {"description": "Pro features section title"}, "syncPhotosAndFiles": "Sync Photos and Files", "@syncPhotosAndFiles": {"description": "Feature title about sync files"}, "syncPhotosAndFilesDescription": "Get files across devices, available on web and reports.", "@syncPhotosAndFilesDescription": {"description": "Feature description about sync files"}, "pdfReports": "PDF Reports", "@pdfReports": {"description": "Feature title about pdf reports"}, "pdfReportsDescription": "Generate automated reports from your entries.", "@pdfReportsDescription": {"description": "Feature description about pdf reports"}, "entriesNotYetSyncToastError": "Entries not yet synchronized", "@entriesNotYetSyncToastError": {"description": "Error message when trying to generate a report from records that are not synchronized."}, "pasteErrorToast": "Error pasting from clipboard. Try again.", "@pasteErrorToast": {"description": "Shows in a small toast when failing to paste from clipboard."}, "powerTools": "Power Tools", "@powerTools": {"description": "Feature title about power tools"}, "powerToolsDescription": "QR codes, signatures, charts, map tracking, boards, custom alerts and more.", "@powerToolsDescription": {"description": "Feature description about power tools"}, "onboarding1Title": "Organized entries, automatic reports.", "@onboarding1Title": {"description": "Onboarding page one title text."}, "onboarding2Title": "Record everything.", "@onboarding2Title": {"description": "Onboarding page two title text."}, "onboarding2Subtitle": "Capture notes, activities, work and evidence - organized for easy access later.", "@onboarding2Subtitle": {"description": "Onboarding page two subtitle text."}, "onboarding3Title": "Create automatic reports.", "@onboarding3Title": {"description": "Onboarding page three title text."}, "onboarding3Subtitle": "Create and share reports in PDF or Excel to share with collaborators and clients - you define what to include.", "@onboarding3Subtitle": {"description": "Onboarding page three subtitle text."}, "onboarding4Title": "Professionalize your work and reduce time with powerful tools.", "@onboarding4Title": {"description": "Onboarding page four title text."}, "knowAllFeatures": "Discover Bitacora.io", "@knowAllFeatures": {"description": "Text button to navigate to functions web page from pro features page"}, "noLongerAbleToEdit": "No longer able to edit this entry.", "@noLongerAbleToEdit": {"description": "Error message when an entry is no longer editable."}, "noPermissionToEdit": "You don't have permission to edit this entry.", "@noPermissionToEdit": {"description": "Error message when there is no permission to edit an entry."}, "wrongDateForEntry": "Unable to save entry on the selected date.", "@wrongDateForEntry": {"description": "Error message when there is no permission to save entry because of wrong entry date."}, "aiAssistant": "Ai Assistant", "@aiAssistant": {"description": "Feature title about ai assistant"}, "experimentalModeEnabled": "Experimental mode enabled", "@experimentalModeEnabled": {"description": "Experimental model easter egg enabled"}, "experimentalModeDisabled": "Experimental mode disabled", "@experimentalModeDisabled": {"description": "Experimental model easter egg disabled"}, "transcript": "Transcript:", "@transcript": {"description": "Label for the transcript section in the transcript notification"}, "sentiment": "Sentiment:", "@sentiment": {"description": "Label for the sentiment section in the transcript notification"}, "keywords": "Keywords:", "@keywords": {"description": "Label for the keywords section in the transcript notification"}, "actionItems": "Action Items:", "@actionItems": {"description": "Label for the action items section in the transcript notification"}, "aiGeneratedNotification": "This entry was created from an audio recording, photos or video.", "@audioTranscriptNotification": {"description": "Text shown in the transcript notification header"}, "aiResourceNotSelected": "No resource was selected to generate an entry", "@aiResourceNotSelected": {"description": "Text indicating that no resource was selected for the AI generator."}}