// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appMotto => 'Your business\'s diary';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get days => 'days';

  @override
  String get lastWeek => 'Last week';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get anHourAgo => 'An hour ago';

  @override
  String get aMinuteAgo => 'A minute ago';

  @override
  String get justNow => 'Just now';

  @override
  String get anHourLeft => 'An 1h left';

  @override
  String get aMinuteLeft => 'A 1m ago';

  @override
  String get email => 'Email';

  @override
  String get emails => 'Emails';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get signup => 'Signup';

  @override
  String get reset => 'Reset';

  @override
  String get back => 'Back';

  @override
  String get cancel => 'Cancel';

  @override
  String get grant => 'Grant';

  @override
  String get edit => 'Edit';

  @override
  String get finish => 'Finish';

  @override
  String get update => 'Update';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get type => 'Type';

  @override
  String get date => 'Date';

  @override
  String get birthday => 'Birthday';

  @override
  String get status => 'Status';

  @override
  String get progress => 'Progress';

  @override
  String get completed => 'Completed';

  @override
  String get failed => 'Failed';

  @override
  String get download => 'Download';

  @override
  String get open => 'Open';

  @override
  String get openWith => 'Open with';

  @override
  String get tag => 'Tag';

  @override
  String get tags => 'Tags';

  @override
  String get sync => 'Sync';

  @override
  String get syncing => 'Syncing';

  @override
  String get welcome => 'Welcome';

  @override
  String get address => 'Address';

  @override
  String get addresses => 'Addresses';

  @override
  String get latitude => 'Latitude';

  @override
  String get longitude => 'Longitude';

  @override
  String get send => 'Send';

  @override
  String get sending => 'Sending';

  @override
  String get sent => 'Sent';

  @override
  String get share => 'Share';

  @override
  String get sharing => 'Sharing';

  @override
  String get staff => 'Staff';

  @override
  String get simple => 'Simple';

  @override
  String get switch_ => 'Switch';

  @override
  String get invite => 'Invite';

  @override
  String get inProgress => 'In Progress...';

  @override
  String get resend => 'Resend';

  @override
  String get deactivate => 'Deactivate';

  @override
  String get audioRecording => 'Audio Recording';

  @override
  String get voiceRecognition => 'Voice Recognition';

  @override
  String get speechNote => 'Speech Note';

  @override
  String get trackerEntry => 'Tracked Entry';

  @override
  String get other => 'Other';

  @override
  String get otherSingular => 'Other';

  @override
  String get feed => 'Feed';

  @override
  String get posts => 'Posts';

  @override
  String get resources => 'Resources';

  @override
  String get readMore => 'Read more';

  @override
  String get report => 'Report';

  @override
  String get reports => 'Reports';

  @override
  String get spreadsheet => 'Spreadsheet';

  @override
  String get creating => 'Creating';

  @override
  String get processing => 'Processing';

  @override
  String get opening => 'Opening';

  @override
  String get today => 'Today';

  @override
  String get elapsed => 'Elapsed';

  @override
  String get start => 'Start';

  @override
  String get stop => 'Stop';

  @override
  String get notifications => 'Notifications';

  @override
  String get title => 'Title';

  @override
  String get subtitle => 'Subtitle';

  @override
  String get text => 'Text';

  @override
  String get view => 'View';

  @override
  String get all => 'All';

  @override
  String get allWithTemplates => 'All (with Templates)';

  @override
  String get clear => 'Clear';

  @override
  String get clearAll => 'Clear All';

  @override
  String get assignee => 'Assignee';

  @override
  String get signee => 'Signee';

  @override
  String get creator => 'Creator';

  @override
  String get client => 'Client';

  @override
  String get include => 'Include';

  @override
  String get customize => 'Customize';

  @override
  String get template => 'Template';

  @override
  String get retry => 'Retry';

  @override
  String get retrieve => 'Retrieve';

  @override
  String get project => 'Project';

  @override
  String get company => 'Company';

  @override
  String get area => 'Area';

  @override
  String get approved => 'Approved';

  @override
  String get rejected => 'Rejected';

  @override
  String get received => 'Received';

  @override
  String get new_ => 'New';

  @override
  String get create => 'Create';

  @override
  String get missing => 'Missing';

  @override
  String get empty => 'Empty';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get longPress => 'Long Press';

  @override
  String get discard => 'Discard';

  @override
  String get entries => 'Entries';

  @override
  String get home => 'Home';

  @override
  String get office => 'Office';

  @override
  String get work => 'Work';

  @override
  String get mobile => 'Mobile';

  @override
  String get main => 'Main';

  @override
  String get oneHour => '1 hour';

  @override
  String nHours(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString hours';
  }

  @override
  String get group => 'Group';

  @override
  String get ungroup => 'Ungroup';

  @override
  String daysAgo(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString days ago';
  }

  @override
  String hoursAgo(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString hours ago';
  }

  @override
  String minutesAgo(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString minutes ago';
  }

  @override
  String secondsAgo(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString seconds ago';
  }

  @override
  String hoursLeft(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '${nString}h left';
  }

  @override
  String minutesLeft(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '${nString}m left';
  }

  @override
  String secondsLeft(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '${nString}s left';
  }

  @override
  String get willStopSoon => 'Will stop soon';

  @override
  String get permissionRequired => 'Permission Required';

  @override
  String get trackingConsent => 'Bitacora.io collects location data to enable route tracking initiated by the user even when app is closed or not in use.';

  @override
  String relatedEntries(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString related entries';
  }

  @override
  String get newReport => 'New Report';

  @override
  String get reportIncludeCaption => 'Select which entry fields to include in your report.';

  @override
  String get reportCustomizeCaption => 'Add a header to your report.';

  @override
  String get reportFileCouldNotBeFound => 'The report file could not be found.';

  @override
  String get noAppToOpen => 'No app found to open this file.';

  @override
  String get filters => 'Filters';

  @override
  String get addFilters => 'Add Filters';

  @override
  String get noActiveFilters => 'No active filters';

  @override
  String get reportFiltersCaption => 'Apply filters to view selected entries.';

  @override
  String get externalSignee => '-External-';

  @override
  String get signatureType => 'Signature Type';

  @override
  String get search => 'Search';

  @override
  String get searchInput => 'Input exact word or phrase for search...';

  @override
  String get noResults => 'No results';

  @override
  String get format => 'Format';

  @override
  String get period => 'Period';

  @override
  String nDays(int n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return '$nString days';
  }

  @override
  String distanceTraveled(String n, String unit) {
    return 'Distance traveled: $n $unit';
  }

  @override
  String averageSpeed(double n, String unit) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String nString = nNumberFormat.format(n);

    return 'Average speed: $nString $unit';
  }

  @override
  String get location => 'Location';

  @override
  String get networkError => 'There was an issue with the network.';

  @override
  String get forgotPassword => 'Forgot password?';

  @override
  String get resetPassword => 'Reset password';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get name => 'Name';

  @override
  String get phoneNumber => 'Phone number';

  @override
  String get phoneNumbers => 'Phone numbers';

  @override
  String get organizations => 'Organizations';

  @override
  String get proPlan => 'Pro Plan';

  @override
  String get failedUserInvite => '⚠️ Error sending invite';

  @override
  String copyClipboard(String text) {
    return 'Copied $text.';
  }

  @override
  String get activeOrganization => 'Active Organization';

  @override
  String get pdfOrExcel => 'PDF or Excel';

  @override
  String get createAndShareReport => 'Create & Share PDF';

  @override
  String get reportScreenTitle => 'Looking to create reports or export your data?';

  @override
  String get reportScreenMessage => 'Visit the Bitacora.io web panel to manage your information.';

  @override
  String get reportTemplateBaseDescription => 'Regular / Day';

  @override
  String get reportTemplateCompactDescription => 'Compact / Hr';

  @override
  String get reportTemplateCompactMinimalDescription => 'Compact Minimal';

  @override
  String get reportTemplateCompactTwoColumnsDescription => 'Compact / 2 Columns';

  @override
  String get reportFailedToCreate => 'Failed to create report.';

  @override
  String syncDescription(String updatedAt) {
    return 'Updated $updatedAt';
  }

  @override
  String get loginRequired => 'Login Required';

  @override
  String get updateRequired => 'Update Required';

  @override
  String get incompleteSync => 'There are logs that have not yet synced. Make sure you have good internet and tap Sync.';

  @override
  String get syncInProgress => 'Sync in Progress.';

  @override
  String get syncReport => 'There still appear to be records to be synced in your organization, your report may be incomplete.';

  @override
  String get sessionExpired => 'The session has expired.\nEnter your password to sync your data.';

  @override
  String get apiExpired => 'This app version is no longer supported.\nUpdate to sync your data.';

  @override
  String get leaveForLater => 'Leave for later';

  @override
  String get youWontBeAbleToSyncData => 'You won\'t be able to sync data';

  @override
  String get credentialNotMatchWithCurrentUser => 'The credentials don\'t match with the current user.';

  @override
  String get userSettings => 'User settings';

  @override
  String get includeOnEntryCapture => 'Include on entry capture';

  @override
  String get locationTrackingActive => 'Location tracking active';

  @override
  String locationTrackingFromAnotherOrg(String organizationName) {
    return 'The tracking entry belongs to $organizationName.';
  }

  @override
  String get myActiviy => 'My Activiy';

  @override
  String get myActiviyDescription => 'View the number of entries created this week and this month';

  @override
  String get weekChartTitle => 'This Week';

  @override
  String get weekChartSubtitle => 'Compared to Last Week';

  @override
  String get monthChartTitle => 'This Month';

  @override
  String monthChartSubtitle(int count) {
    return 'entries compared to $count Last Month';
  }

  @override
  String get allowTrackingUser => 'Track My Location';

  @override
  String get forThisOrganization => 'For this organization';

  @override
  String forOrganization(String organizationName) {
    return 'For $organizationName';
  }

  @override
  String get backgroundLocationTracking => 'Background location tracking active. Tap to open.';

  @override
  String get systemPermissions => 'System permissions';

  @override
  String get phoneInputCaption => 'For improved support & upcoming WhatsApp integration.';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get videoTutorials => 'Video Tutorials';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get sendUsWhatsApp => 'WhatsApp Support';

  @override
  String get emailSupport => 'Email Support';

  @override
  String supportMessage(String email) {
    return 'Hi, I\'m a Bitacora.io user $email, I have a question or comment...';
  }

  @override
  String get loginSupportMessage => 'Hi, I have a question or comment...';

  @override
  String get theme => 'Theme';

  @override
  String get system => 'System';

  @override
  String get light => 'Light';

  @override
  String get dark => 'Dark';

  @override
  String get allProjects => 'All: Projects / Categories / Locations';

  @override
  String get menu => 'Menu';

  @override
  String get noScheduledTasks => 'No scheduled tasks.';

  @override
  String get log => 'Log';

  @override
  String get inventory => 'Inventory';

  @override
  String get personnel => 'Personnel';

  @override
  String get movement => 'Movement';

  @override
  String get outgoing => 'Outgoing';

  @override
  String get incoming => 'Incoming';

  @override
  String get invalidEmail => 'Invalid email';

  @override
  String get alreadyUsedEmail => 'Email already used in this organization';

  @override
  String get invalidPhoneNumber => 'Invalid phone number';

  @override
  String get emptyName => 'Empty name';

  @override
  String get emptyPassword => 'Empty password';

  @override
  String get somethingWentWrong => 'Something went wrong.';

  @override
  String get tryAgainLater => 'Try again later.';

  @override
  String get requiredField => 'This field is required.';

  @override
  String get formQuantity => '# / Qtty.';

  @override
  String get formTitle => 'Activity / Entry Title';

  @override
  String get formProject => 'Proj., Category or Location';

  @override
  String get formSublocation => 'Sub-proj. cat. loc';

  @override
  String get formComments => 'Comments / Description';

  @override
  String get progressComments => 'Progress Comments / Description';

  @override
  String get locationDenied => 'Location permissions are denied.';

  @override
  String get photoDenied => 'Photo gallery access is denied.';

  @override
  String get videoDenied => 'Video gallery access is denied.';

  @override
  String get fileDenied => 'Storage access is denied.';

  @override
  String get cameraDenied => 'Camera access is denied.';

  @override
  String get recordingDenied => 'Audio recording permissions are denied.';

  @override
  String get voiceRecognitionError => '⚠️ Error due to speech recognition.';

  @override
  String get goToAppSystemSettings => 'Go to Settings';

  @override
  String get newEntries => 'New entries';

  @override
  String get offered => 'Offered';

  @override
  String get itemName => 'Item Name';

  @override
  String get from_ => 'From:';

  @override
  String get to_ => 'To:';

  @override
  String get recipient => 'Recipient';

  @override
  String get provider => 'Provider';

  @override
  String get unit_ => 'UNIT';

  @override
  String get total_ => 'TOT';

  @override
  String get expense => 'Expense';

  @override
  String get income => 'Income';

  @override
  String get reason => 'Reason';

  @override
  String get na => 'N/A';

  @override
  String get paid => 'PAID';

  @override
  String get unpaid => 'NOT PAID';

  @override
  String get numHours_ => '# OF HOURS';

  @override
  String get time_ => 'Time';

  @override
  String get hours => 'Hours';

  @override
  String get entrance => 'Entrance';

  @override
  String get exit => 'Exit';

  @override
  String get scheduleEntry => 'Schedule Entry';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get startTime => 'Start Time';

  @override
  String get endTime => 'End Time';

  @override
  String get timer => 'Timer';

  @override
  String get entryTimer => 'Entry Timer';

  @override
  String get moreThanOneTimer => 'You can\'t have more than one active timer.';

  @override
  String get moreThanOneLocationTracking => 'You can\'t have more than one tracking entry.';

  @override
  String get progressive => 'Progressive';

  @override
  String get complete => 'Complete';

  @override
  String get myFirstReportIsReady => 'Your first PDF report is ready!';

  @override
  String get scheduledEntry => 'Scheduled Entry';

  @override
  String get incomeOrExpense => 'Income and/or expense';

  @override
  String get addAComment => 'Add a comment';

  @override
  String get assignedTo => 'Assigned to';

  @override
  String get assignedToMe => 'Assigned to me';

  @override
  String get moreFields => 'More Fields';

  @override
  String get logoutTitle => 'Logout';

  @override
  String get logoutMessage => 'Are you sure that you want to log out?';

  @override
  String get logoutWillLoseData => '*You have local changes, they will be permanently lost.';

  @override
  String get chatWillBeLost => 'The chat will be lost on exit.';

  @override
  String get writeSomething => 'Write something...';

  @override
  String get entryNotSaved => 'Entry not saved';

  @override
  String get doodleNotSaved => 'Doodle not saved';

  @override
  String get willLoseData => 'How do you want to proceed?';

  @override
  String get chooseYourOrg => 'Choose your active organization';

  @override
  String get wereInvitedOrg => 'You were invited to a new organization';

  @override
  String get switchOrganization => 'Switch Organization';

  @override
  String get reportEntryType => 'Entry Type';

  @override
  String get photoPdf => 'Photo PDF';

  @override
  String get startDateEndDate => 'Start Date / End Date';

  @override
  String get incomeExpense => 'Income / Expense';

  @override
  String get providerStatus => 'Provider / Status';

  @override
  String get startTomorrow => 'Start tomorrow';

  @override
  String get finishToday => 'Finish today';

  @override
  String get finishTomorrow => 'Finish tomorrow';

  @override
  String get scheduledEntries => 'Scheduled Entries';

  @override
  String get notificationTime => 'Notification time';

  @override
  String get notifyDayOf => 'Notify the day of';

  @override
  String get alertOnTheStartAndOrEndDayOfTheTask => 'Alert on the start and/or end day of the task.';

  @override
  String get notifyTheDayBefore => 'Notify the day before';

  @override
  String get alertTheDayBeforeTheTaskStartsAndOrEnds => 'Alert the day before the task starts and/or ends.';

  @override
  String get dailyReminder => 'Daily reminder';

  @override
  String get dailyReminderDescription => 'Notifies daily reminding you to log your notes and evidences';

  @override
  String get logYourActivitiesOfToday => 'Log your activities of today';

  @override
  String get dailyReminderBody => 'Don’t forget to log today’s notes and evidences so you don’t pile up work.';

  @override
  String get usePreviousVersion => 'Use Previous Version';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get bugReportChannelDescription => 'Notifies when your bug reports are ready and allows you to share them.';

  @override
  String get bugReportTapToShare => 'Tap to share.';

  @override
  String get bugReportHint => 'Please describe your issue.';

  @override
  String get deleteAccountTitle => 'Delete Account';

  @override
  String get deleteAccountMessage => 'This will initiate the process of deleting your account and log you out.';

  @override
  String get farFromQr => 'You are too far from the QR code location';

  @override
  String get qrDoesNotBelongOrg => 'This QR does not belong to your current active organization';

  @override
  String get deleteTitle => 'Are you sure?';

  @override
  String get deleteMessage => 'This will permanently delete the entry.';

  @override
  String get deleteSelectionTitle => 'Are you sure?';

  @override
  String get deleteSelectionMessage => 'This will permanently delete the entries.';

  @override
  String get ungroupGroupTitle => 'Are you sure?';

  @override
  String get ungroupGroupMessage => 'This will permanently ungroup all entries.';

  @override
  String selectedEntries(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString selected entries',
      one: '$countString selected entry',
    );
    return '$_temp0';
  }

  @override
  String selectedNames(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count selected names',
      one: '$count selected name',
    );
    return '$_temp0';
  }

  @override
  String get deleteReport => 'This will permanently delete the selected report(s).';

  @override
  String get freePlan => 'Free Plan';

  @override
  String get freePlanDescription => 'Record your activity notes day by day and view it on the web and app.';

  @override
  String get trialPlan => 'Trial Plan';

  @override
  String get trialPlanDescription => 'Capture, create reports and add collaborators to explore on the web and app.';

  @override
  String get discoverBitacora => 'Start discovering Bitacora.io:';

  @override
  String get syncPhotosAndFiles => 'Sync Photos and Files';

  @override
  String get syncPhotosAndFilesDescription => 'Get files across devices, available on web and reports.';

  @override
  String get pdfReports => 'PDF Reports';

  @override
  String get pdfReportsDescription => 'Generate automated reports from your entries.';

  @override
  String get entriesNotYetSyncToastError => 'Entries not yet synchronized';

  @override
  String get pasteErrorToast => 'Error pasting from clipboard. Try again.';

  @override
  String get powerTools => 'Power Tools';

  @override
  String get powerToolsDescription => 'QR codes, signatures, charts, map tracking, boards, custom alerts and more.';

  @override
  String get onboarding1Title => 'Organized entries, automatic reports.';

  @override
  String get onboarding2Title => 'Record everything.';

  @override
  String get onboarding2Subtitle => 'Capture notes, activities, work and evidence - organized for easy access later.';

  @override
  String get onboarding3Title => 'Create automatic reports.';

  @override
  String get onboarding3Subtitle => 'Create and share reports in PDF or Excel to share with collaborators and clients - you define what to include.';

  @override
  String get onboarding4Title => 'Professionalize your work and reduce time with powerful tools.';

  @override
  String get knowAllFeatures => 'Discover Bitacora.io';

  @override
  String get noLongerAbleToEdit => 'No longer able to edit this entry.';

  @override
  String get noPermissionToEdit => 'You don\'t have permission to edit this entry.';

  @override
  String get wrongDateForEntry => 'Unable to save entry on the selected date.';

  @override
  String get aiAssistant => 'Ai Assistant';

  @override
  String get experimentalModeEnabled => 'Experimental mode enabled';

  @override
  String get experimentalModeDisabled => 'Experimental mode disabled';

  @override
  String get transcript => 'Transcript:';

  @override
  String get sentiment => 'Sentiment:';

  @override
  String get keywords => 'Keywords:';

  @override
  String get actionItems => 'Action Items:';

  @override
  String get aiGeneratedNotification => 'This entry was created from an audio recording, photos or video.';

  @override
  String get aiResourceNotSelected => 'No resource was selected to generate an entry';
}
