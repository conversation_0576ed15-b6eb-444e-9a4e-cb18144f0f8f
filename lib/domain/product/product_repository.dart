import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/product/product_fields_builder.dart';
import 'package:bitacora/domain/user/user.dart';

abstract class ProductRepository<C extends RepositoryQueryContext,
    F extends ProductFieldsBuilder> extends RepositoryTable<Product, C, F> {
  Future<List<Product>> findAll(C context);

  Future<List<Product>> findByOrganization(C context, LocalId organizationId);

  Future<List<User>> findUsersByProductId(C context, LocalId productId);

  Future<bool> hasProduct(
    C context,
    LocalId userId,
    LocalId organizationId,
    ProductUuid productUuid,
  );
}
