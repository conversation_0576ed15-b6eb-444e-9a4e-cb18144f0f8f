import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/address/address.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/avatar/avatar.dart';
import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/email/email.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/feature_flag/feature_flag.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/phone/phone.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/domain/report_template/report_template.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource_category/resource_category.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/domain/template_condition/template_condition.dart';
import 'package:bitacora/domain/template_group/template_group.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/domain/worklog/worklog.dart';

abstract class Translator {
  ModelTranslator<Access> get access;

  ModelTranslator<Address> get address;

  ModelTranslator<Attachment> get attachment;

  ModelTranslator<Avatar> get avatar;

  ModelTranslator<CustomField> get customField;

  ModelTranslator<CustomFieldMetadata> get customFieldMetadata;

  ModelTranslator<CustomFieldOptions> get customFieldOptions;

  ModelTranslator<CustomFieldAllowedValue> get customFieldAllowedValue;

  ModelTranslator<Entry> get entry;

  ModelTranslator<Email> get email;

  ModelTranslator<EntryGroup> get entryGroup;

  ModelTranslator<EntryGroupEntry> get entryGroupEntry;

  ModelTranslator<EntryMetadata> get entryMetadata;

  ModelTranslator<FeatureFlag> get featureFlag;

  ModelTranslator<FeedPost> get feedPost;

  ModelTranslator<Inventorylog> get inventorylog;

  ModelTranslator<OpenState> get openState;

  ModelTranslator<Organization> get organization;

  ModelTranslator<OutgoingMutation> get outgoingMutation;

  ModelTranslator<Person> get person;

  ModelTranslator<PersonDetail> get personDetail;

  ModelTranslator<Personnellog> get personnellog;

  ModelTranslator<Phone> get phone;

  ModelTranslator<Product> get product;

  ModelTranslator<Progresslog> get progresslog;

  ModelTranslator<Templatelog> get templatelog;

  ModelTranslator<Project> get project;

  ModelTranslator<QrCode> get qrCode;

  ModelTranslator<Report> get report;

  ModelTranslator<ReportTemplate> get reportTemplate;

  ModelTranslator<Resource> get resource;

  ModelTranslator<ResourceCategory> get resourceCategory;

  ModelTranslator<Signature> get signature;

  ModelTranslator<Tag> get tag;

  ModelTranslator<Template> get template;

  ModelTranslator<TemplateBlock> get templateBlock;

  ModelTranslator<TemplateCondition> get templateCondition;

  ModelTranslator<TemplateGroup> get templateGroup;

  ModelTranslator<User> get user;

  ModelTranslator<Worklog> get worklog;

  ModelTranslator<LocationTracking> get locationTracking;

  ModelTranslator<LocationPoint> get locationPoint;
}
