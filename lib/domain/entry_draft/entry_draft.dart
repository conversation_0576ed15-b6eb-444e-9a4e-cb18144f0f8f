import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_draft/value/entry_draft_created_at.dart';
import 'package:bitacora/domain/entry_draft/value/entry_draft_updated_at.dart';

export 'package:bitacora/domain/entry_draft/value/entry_draft_created_at.dart';
export 'package:bitacora/domain/entry_draft/value/entry_draft_updated_at.dart';

class EntryDraft extends Model {
  final Entry? entry;
  final EntryDraftCreatedAt? createdAt;
  final EntryDraftUpdatedAt? updatedAt;

  const EntryDraft({
    super.id,
    this.entry,
    this.createdAt,
    this.updatedAt,
  }) : super(remoteId: null);

  EntryDraft copyWith({
    LocalId? id,
    Entry? entry,
    EntryDraftCreatedAt? createdAt,
    EntryDraftUpdatedAt? updatedAt,
  }) {
    return EntryDraft(
      id: id ?? this.id,
      entry: entry ?? this.entry,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        EntryDraftField.id: id,
        EntryDraftField.entry: entry,
        EntryDraftField.createdAt: createdAt,
        EntryDraftField.updatedAt: updatedAt,
      };
}

enum EntryDraftField with Field {
  id,
  entry,
  createdAt,
  updatedAt,
}

const entryDraftNestedModelFields = {EntryDraftField.entry};
