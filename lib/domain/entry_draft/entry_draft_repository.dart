import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_draft/entry_draft_fields_builder.dart';

abstract class EntryDraftRepository<C extends RepositoryQueryContext,
        F extends EntryDraftFieldsBuilder>
    extends RepositoryTable<EntryDraft, C, F> {
  Future<EntryDraft?> findByEntry(C context, LocalId entryId);

  Future<void> deleteByEntry(C context, LocalId entryId);

  Future<void> convertToPermanentEntry(C context, LocalId draftId);
}
