import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class EntryFieldsBuilder {
  EntryFieldsBuilder remoteId();

  EntryFieldsBuilder day();

  EntryFieldsBuilder time();

  EntryFieldsBuilder startDate();

  EntryFieldsBuilder endDate();

  EntryFieldsBuilder startTime();

  EntryFieldsBuilder endTime();

  EntryFieldsBuilder comments();

  EntryFieldsBuilder createdAt();

  EntryFieldsBuilder updatedAt();

  EntryFieldsBuilder syncVersion();

  EntryFieldsBuilder timerStatus();

  EntryFieldsBuilder location();

  EntryFieldsBuilder assignee(Fields fields);

  EntryFieldsBuilder author(Fields fields);

  EntryFieldsBuilder inventorylog(Fields fields);

  EntryFieldsBuilder personnellog(Fields fields);

  EntryFieldsBuilder worklog(Fields fields);

  EntryFieldsBuilder progresslog(Fields fields);

  EntryFieldsBuilder templatelog(Fields fields);

  EntryFieldsBuilder openState(Fields fields);

  EntryFieldsBuilder entryGroupEntry(Fields fields);

  EntryFieldsBuilder attachments(Fields fields);

  EntryFieldsBuilder tags(Fields fields);

  EntryFieldsBuilder locationTracking(Fields fields);

  EntryFieldsBuilder signatures(Fields fields);

  EntryFieldsBuilder source(Fields fields);

  EntryFieldsBuilder metadata(Fields fields);

  EntryFieldsBuilder entryDraft(Fields fields);

  Fields build();
}
