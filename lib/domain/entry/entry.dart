import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/value/entry_comments.dart';
import 'package:bitacora/domain/entry/value/entry_created_at.dart';
import 'package:bitacora/domain/entry/value/entry_sync_version.dart';
import 'package:bitacora/domain/entry/value/entry_timer_status.dart';
import 'package:bitacora/domain/entry/value/entry_updated_at.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:collection/collection.dart';

export 'package:bitacora/domain/common/value_object/common.dart';
export 'package:bitacora/domain/entry/value/entry_comments.dart';
export 'package:bitacora/domain/entry/value/entry_created_at.dart';
export 'package:bitacora/domain/entry/value/entry_sync_version.dart';
export 'package:bitacora/domain/entry/value/entry_updated_at.dart';

const kEntrySyncVersion = 2;

class Entry extends Model {
  final LogDay? day;
  final LogTime? time;
  final NullableLogDay? startDate;
  final NullableLogDay? endDate;
  final NullableLogTime? startTime;
  final NullableLogTime? endTime;
  final LatLngValueObject? location;
  final EntryComments? comments;
  final EntryCreatedAt? createdAt;
  final EntryUpdatedAt? updatedAt;
  final EntrySyncVersion? syncVersion;
  final EntryTimerStatus? timerStatus;

  final User? assignee;
  final User? author;
  final Extension? extension;
  final OpenState? openState;
  final EntryGroupEntry? entryGroupEntry;
  final List<Attachment>? attachments;
  final List<Tag>? tags;
  final LocationTracking? locationTracking;
  final List<Signature>? signatures;
  final EntrySource? source;
  final List<EntryMetadata>? metadata;
  final EntryDraft? entryDraft;

  const Entry({
    super.id,
    super.remoteId,
    this.day,
    this.time,
    this.startDate,
    this.endDate,
    this.startTime,
    this.endTime,
    this.location,
    this.comments,
    this.createdAt,
    this.updatedAt,
    this.syncVersion,
    this.timerStatus,
    this.assignee,
    this.author,
    this.extension,
    this.openState,
    this.entryGroupEntry,
    this.attachments,
    this.tags,
    this.locationTracking,
    this.signatures,
    this.source,
    this.metadata,
    this.entryDraft,
  });

  Entry copyWith({
    LocalId? id,
    RemoteId? remoteId,
    LogDay? day,
    LogTime? time,
    NullableLogDay? startDate,
    NullableLogDay? endDate,
    NullableLogTime? startTime,
    NullableLogTime? endTime,
    LatLngValueObject? location,
    EntryComments? comments,
    EntryCreatedAt? createdAt,
    EntryUpdatedAt? updatedAt,
    EntrySyncVersion? syncVersion,
    EntryTimerStatus? timerStatus,
    User? assignee,
    User? author,
    Extension? extension,
    OpenState? openState,
    EntryGroupEntry? entryGroupEntry,
    List<Attachment>? attachments,
    List<Tag>? tags,
    LocationTracking? locationTracking,
    List<Signature>? signatures,
    EntrySource? source,
    List<EntryMetadata>? metadata,
    EntryDraft? entryDraft,
  }) {
    return Entry(
      id: id ?? this.id,
      remoteId: remoteId ?? this.remoteId,
      day: day ?? this.day,
      time: time ?? this.time,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      comments: comments ?? this.comments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncVersion: syncVersion ?? this.syncVersion,
      timerStatus: timerStatus ?? this.timerStatus,
      assignee: assignee ?? this.assignee,
      author: author ?? this.author,
      extension: extension ?? this.extension,
      openState: openState ?? this.openState,
      entryGroupEntry: entryGroupEntry ?? this.entryGroupEntry,
      attachments: attachments ?? this.attachments,
      tags: tags ?? this.tags,
      locationTracking: locationTracking ?? this.locationTracking,
      signatures: signatures ?? this.signatures,
      source: source ?? this.source,
      metadata: metadata ?? this.metadata,
      entryDraft: entryDraft ?? this.entryDraft,
    );
  }

  Worklog? get worklog {
    if (extension?.extensionType == ExtensionType.worklog) {
      return extension as Worklog;
    }
    return null;
  }

  Inventorylog? get inventorylog {
    if (extension?.extensionType == ExtensionType.inventorylog) {
      return extension as Inventorylog;
    }
    return null;
  }

  Personnellog? get personnellog {
    if (extension?.extensionType == ExtensionType.personnellog) {
      return extension as Personnellog;
    }
    return null;
  }

  Progresslog? get progresslog {
    if (extension?.extensionType == ExtensionType.progresslog) {
      return extension as Progresslog;
    }
    return null;
  }

  Templatelog? get templatelog {
    if (extension?.extensionType == ExtensionType.templatelog) {
      return extension as Templatelog;
    }
    return null;
  }

  EntryMetadata? get metadataSentiment => metadata?.firstWhereOrNull(
      (e) => e.type?.value == EntryMetadataTypeValue.sentiment);

  List<EntryMetadata> get metadataKeywords =>
      metadata
          ?.where((e) => e.type?.value == EntryMetadataTypeValue.keyword)
          .toList() ??
      [];

  List<EntryMetadata> get metadataActionItems =>
      metadata
          ?.where((e) => e.type?.value == EntryMetadataTypeValue.actionItem)
          .toList() ??
      [];

  List<EntryMetadata> get metadataHealthItems =>
      metadata
          ?.where((e) => e.type?.value == EntryMetadataTypeValue.healthItem)
          .toList() ??
      [];

  List<EntryMetadata> get metadataHealthTags =>
      metadata
          ?.where((e) => e.type?.value == EntryMetadataTypeValue.healthTag)
          .toList() ??
      [];

  List<Project> get projects {
    switch (extension!.extensionType) {
      case ExtensionType.worklog:
        return [worklog!.project!];
      case ExtensionType.inventorylog:
        final projects = <Project>[];
        if (inventorylog!.destProject != null) {
          projects.add(inventorylog!.destProject!);
        }
        if (inventorylog!.sourceProject != null) {
          projects.add(inventorylog!.sourceProject!);
        }
        return projects;
      case ExtensionType.personnellog:
        return [personnellog!.project!];
      case ExtensionType.progresslog:
        return progresslog!.entry!.projects;
      case ExtensionType.templatelog:
        return templatelog!.projects;
      default:
        throw Exception(
            'Extension type ${extension!.extensionType} not supported');
    }
  }

  bool get hasSimplelog =>
      extension!.extensionType == ExtensionType.worklog &&
      worklog!.project!.name?.value == kDefaultProjectName;

  bool get hasStartedTimer =>
      timerStatus == EntryTimerStatus.started &&
      startDate?.value != null &&
      startTime?.value != null;

  @override
  Map<Field, dynamic> get fields => {
        EntryField.id: id,
        EntryField.remoteId: remoteId,
        EntryField.day: day,
        EntryField.time: time,
        EntryField.startDate: startDate,
        EntryField.endDate: endDate,
        EntryField.startTime: startTime,
        EntryField.endTime: endTime,
        EntryField.location: location,
        EntryField.comments: comments,
        EntryField.createdAt: createdAt,
        EntryField.updatedAt: updatedAt,
        EntryField.syncVersion: syncVersion,
        EntryField.timerStatus: timerStatus,
        EntryField.assignee: assignee,
        EntryField.author: author,
        EntryField.extension: extension,
        EntryField.worklog: worklog,
        EntryField.inventorylog: inventorylog,
        EntryField.personnellog: personnellog,
        EntryField.progresslog: progresslog,
        EntryField.templatelog: templatelog,
        EntryField.openState: openState,
        EntryField.entryGroupEntry: entryGroupEntry,
        EntryField.attachments: attachments,
        EntryField.tags: tags,
        EntryField.locationTracking: locationTracking,
        EntryField.signatures: signatures,
        EntryField.source: source,
        EntryField.metadata: metadata,
        EntryField.entryDraft: entryDraft,
      };
}

enum EntryField with Field {
  id,
  remoteId,
  day,
  time,
  startDate,
  endDate,
  startTime,
  endTime,
  location,
  comments,
  createdAt,
  updatedAt,
  syncVersion,
  timerStatus,
  assignee,
  author,
  extension,
  worklog,
  inventorylog,
  personnellog,
  progresslog,
  templatelog,
  openState,
  entryGroupEntry,
  attachments,
  tags,
  locationTracking,
  signatures,
  source,
  metadata,
  entryDraft,
}

const entryNestedModelFields = {
  EntryField.assignee,
  EntryField.author,
  EntryField.extension,
  EntryField.openState,
  EntryField.entryGroupEntry,
  EntryField.attachments,
  EntryField.tags,
  EntryField.locationTracking,
  EntryField.signatures,
  EntryField.source,
  EntryField.metadata,
  EntryField.entryDraft,
};
