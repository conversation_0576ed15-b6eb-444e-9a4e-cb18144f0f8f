import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_draft/entry_draft_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_contract.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_translator.dart';

class EntryDraftDbTable extends DbTable<EntryDraft, EntryDraftDbFieldsBuilder>
    implements EntryDraftRepository<DbContext, EntryDraftDbFieldsBuilder> {
  final DbTranslator<EntryDraft> _translator;

  EntryDraftDbTable([this._translator = const EntryDraftDbTranslator()]);

  @override
  EntryDraftDbContract get contract => const EntryDraftDbContract();

  @override
  EntryDraftDbFieldsBuilder get fieldsBuilder => throw UnimplementedError();

  @override
  // TODO: implement translator
  DbTranslator<EntryDraft> get translator => throw UnimplementedError();

  @override
  Future<EntryDraft?> findByEntry(DbContext context, LocalId entryId) async {
    final rows = await query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );

    if (rows.isEmpty) {
      return null;
    }
    return rows.first;
  }

  @override
  Future<void> deleteByEntry(DbContext context, LocalId entryId) async {
    final draft = await findByEntry(context, entryId);
    if (draft == null) {
      return;
    }

    await delete(context, draft.id!);
  }

  @override
  Future<void> convertToPermanentEntry(
    DbContext context,
    LocalId draftId,
  ) async {
    await context.db.transaction((txContext) async {
      final draft = await find(txContext, draftId);
      if (draft != null && draft.entry != null) {
        await delete(txContext, draftId);

        final entry = draft.entry!;

        final mutation = Mutation<Entry>(
          type: MutationType.update,
          id: entry.id,
          model: entry,
        );

        await context.db.entry.onSyncRequested(txContext, mutation);
      }
    });
  }

// @override
// Future<LocalId?> save(DbContext context, EntryDraft model, {
//   bool requestSync = false,
// }) async {
//   return await context.db.transaction((txContext) async {
//     final entryId = await context.db.entry
//         .save(txContext, model.entry!, requestSync: false);
//
//     final now = DateTime.now();
//     final values = {
//       contract.entryId: entryId!.dbValue,
//       contract.createdAt: model.createdAt?.value.millisecondsSinceEpoch ??
//           now.millisecondsSinceEpoch,
//       contract.updatedAt: model.updatedAt?.value.millisecondsSinceEpoch ??
//           now.millisecondsSinceEpoch,
//     };
//
//     if (model.id != null) {
//       await db.update(
//         contract.tableName,
//         values,
//         '${contract.id} = ?',
//         [model.id!.dbValue],
//       );
//       return model.id;
//     } else {
//       final id = await db.insert(contract.tableName, values);
//       return LocalId(id);
//     }
//   });
// }
}
