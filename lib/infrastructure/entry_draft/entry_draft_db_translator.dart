import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_contract.dart';

class EntryDraftDbTranslator implements DbTranslator<EntryDraft> {
  const EntryDraftDbTranslator();

  @override
  Set<Field> get nestedModelFields => entryDraftNestedModelFields;

  @override
  Future<EntryDraft> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return EntryDraft(
      id: fields[EntryDraftField.id]?.value(map),
      entry: await fields[EntryDraftField.entry]?.nested(context, map),
      createdAt: fields[EntryDraftField.createdAt]?.value(map),
      updatedAt: fields[EntryDraftField.updatedAt]?.value(map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, EntryDraft model) async {
    final map = <String, dynamic>{};
    const contract = EntryDraftDbContract();
    
    addField(map, contract.id, model.id);
    addField(map, contract.createdAt, model.createdAt);
    addField(map, contract.updatedAt, model.updatedAt);

    await saveNestedModel<Entry>(
      context, 
      map, 
      contract.entryId, 
      context.db.entry, 
      model.entry,
    );

    return map;
  }
}
