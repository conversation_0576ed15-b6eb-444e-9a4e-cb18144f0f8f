import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_draft/entry_draft_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_contract.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_fields_builder.dart';

class EntryDraftDbRepository extends DbRepository<EntryDraft, EntryDraftDbFieldsBuilder>
    implements EntryDraftRepository<DbRepositoryQueryContext, EntryDraftDbFieldsBuilder> {
  EntryDraftDbRepository(super.db);

  @override
  EntryDraftDbContract get contract => const EntryDraftDbContract();

  @override
  EntryDraftDbFieldsBuilder createFieldsBuilder() => EntryDraftDbFieldsBuilder();

  @override
  Future<List<EntryDraft>> findAll(DbRepositoryQueryContext context) async {
    final query = '''
      SELECT ${context.fields.columns}
      FROM ${contract.tableName}
      ${context.fields.joins}
    ''';
    
    final rows = await db.query(query);
    return rows.map((row) => context.fields.build(row)).toList();
  }

  @override
  Future<EntryDraft?> findByEntry(DbContext context, LocalId entryId) async {
    final query = '''
      SELECT ${context.fields.columns}
      FROM ${contract.tableName}
      ${context.fields.joins}
      WHERE ${contract.entryId} = ?
    ''';
    
    final rows = await db.query(query, [entryId.dbValue]);
    if (rows.isEmpty) {
      return null;
    }
    return context.fields.build(rows.first);
  }

  @override
  Future<void> deleteByEntry(DbRepositoryQueryContext context, LocalId entryId) async {
    await db.execute(
      'DELETE FROM ${contract.tableName} WHERE ${contract.entryId} = ?',
      [entryId.dbValue],
    );
  }

  @override
  Future<void> convertToPermanentEntry(DbRepositoryQueryContext context, LocalId draftId) async {
    await db.transaction((txContext) async {
      final draft = await find(txContext, draftId);
      if (draft != null && draft.entry != null) {
        // Guardar la entrada como permanente (sin marcar como borrador)
        await context.db.entry.save(txContext, draft.entry!, requestSync: true);
        // Eliminar el borrador
        await delete(txContext, draftId);
      }
    });
  }

  @override
  Future<LocalId?> save(DbRepositoryQueryContext context, EntryDraft model) async {
    return await db.transaction((txContext) async {
      // Primero guardamos la entrada sin solicitar sincronización
      final entryId = await context.db.entry.save(txContext, model.entry!, requestSync: false);
      
      final now = DateTime.now();
      final values = {
        contract.entryId: entryId!.dbValue,
        contract.createdAt: model.createdAt?.value.millisecondsSinceEpoch ?? now.millisecondsSinceEpoch,
        contract.updatedAt: model.updatedAt?.value.millisecondsSinceEpoch ?? now.millisecondsSinceEpoch,
      };
      
      if (model.id != null) {
        await db.update(
          contract.tableName,
          values,
          '${contract.id} = ?',
          [model.id!.dbValue],
        );
        return model.id;
      } else {
        final id = await db.insert(contract.tableName, values);
        return LocalId(id);
      }
    });
  }
}