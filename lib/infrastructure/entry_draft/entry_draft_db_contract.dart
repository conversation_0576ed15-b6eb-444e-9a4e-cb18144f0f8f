import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class EntryDraftDbContract extends DbContract {
  static const String _ = 'ed_';
  static const String _tableName = 'entryDraft';

  final String id = '${_}id';

  const EntryDraftDbContract() : super(_, _tableName);

  String get entryId => '${_}entryId';

  String get createdAt => '${_}createdAt';

  String get updatedAt => '${_}updatedAt';

  @override
  int get initialDbVersion => kDbVersionWithEntryDraftTable;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $entryId INTEGER NOT NULL,
    $createdAt INTEGER NOT NULL,
    $updatedAt INTEGER NOT NULL
  )
  ''';
}
