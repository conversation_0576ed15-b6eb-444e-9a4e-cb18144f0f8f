import 'dart:async';
import 'dart:math';

import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/query/entry_common_db_queries.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/entry_repository.dart';
import 'package:bitacora/domain/entry/filter/entry_filter.dart';
import 'package:bitacora/domain/entry/value/entry_timer_status.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_key.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_helper.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry/entry_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry/entry_db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_extension_searchable_fields.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_contract.dart';
import 'package:bitacora/infrastructure/extension/db_extension_type.dart';
import 'package:bitacora/infrastructure/open_state/open_state_db_contract.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_contract.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:bitacora/infrastructure/tag/tag_db_contract.dart';
import 'package:bitacora/infrastructure/tag/tag_entry_db_contract.dart';
import 'package:bitacora/infrastructure/user/user_db_contract.dart';
import 'package:bitacora/infrastructure/user_organization/user_organization_db_contract.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:collection/collection.dart';
import 'package:sqflite/sqflite.dart';

class EntryDbTable extends DbTable<Entry, EntryDbFieldsBuilder>
    implements EntryRepository<DbContext, EntryDbFieldsBuilder> {
  final DbTranslator<Entry> _translator;

  EntryDbTable([this._translator = const EntryDbTranslator()]);

  @override
  EntryDbContract get contract => const EntryDbContract();

  AccessEntryDbContract get accessEntryContract =>
      const AccessEntryDbContract();

  PersonnellogDbContract get personnellogContract =>
      const PersonnellogDbContract();

  OpenStateDbContract get openStateContract => const OpenStateDbContract();

  ProgresslogDbContract get progresslogContract =>
      const ProgresslogDbContract();

  ProjectEntriesProjectDbContract get projectEntriesProjectContract =>
      const ProjectEntriesProjectDbContract();

  UserDbContract get userContract => const UserDbContract();

  ProjectDbContract get projectContract => const ProjectDbContract();

  @override
  DbTranslator<Entry> get translator => _translator;

  @override
  EntryDbFieldsBuilder get fieldsBuilder => EntryDbFieldsBuilder();

  @override
  Future<List<Entry>> findUnsetPermissionCache(
    DbContext context,
    LogDay? minDay,
    LogDay? maxDay,
  ) async {
    final query = '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM $tableName 
      LEFT JOIN ${accessEntryContract.tableName}
      ON ${contract.id} = ${accessEntryContract.entryId}
      WHERE ${accessEntryContract.permission} IS NULL
      ${minDay == null ? '' : 'AND ${contract.day} >= ?'}
      ${maxDay == null ? '' : 'AND ${contract.day} <= ?'}
      LIMIT 50
    ''';

    return rawQuery(
      context,
      query,
      [
        if (minDay != null) minDay.dbValue,
        if (maxDay != null) maxDay.dbValue,
      ],
    );
  }

  @override
  Future<Entry?> findLast(DbContext context) {
    return takeFirst(
        query(context, limit: 1, orderBy: '${contract.createdAt} DESC'));
  }

  @override
  Future<List<Entry>> findDaylog(
    DbContext context,
    LogDay day,
    bool isGhostEntriesEnabled, [
    bool excludeProgresslog = false,
  ]) async {
    final queryScope = context.queryScope!;
    var entryAlias = 'e';
    final query = '''
      SELECT ${columnsForSelect(context.fields!)}
      ${DbQueryScopeUtils().accessFromJoin(queryScope, entryAlias: entryAlias)}
      LEFT JOIN ${openStateContract.tableName}
       ON ${contract.openStateId} = ${openStateContract.id}
      WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
      AND (${contract.day} = ?
        ${!isGhostEntriesEnabled ? '' : '''
            OR (
              ${contract.openStateId} IS NOT NULL
              AND ${contract.day} <= ?
              AND ${openStateContract.endDay} >= ?
            )
        '''}
      )
      AND (${contract.openStateId} IS NULL 
        ${!isGhostEntriesEnabled ? '' : '''
            OR (${openStateContract.progress} != ?
              AND NOT EXISTS (
                SELECT 1
                FROM ${contract.tableName}
                LEFT JOIN ${progresslogContract.tableName} 
                ON ${contract.extensionId} = ${progresslogContract.id}
                WHERE ${contract.day} = ?
                AND ${contract.extensionType} = ?
                AND ${progresslogContract.entryId} = $entryAlias.${contract.id}
              )
            )
        '''}
      )
      ${!excludeProgresslog ? '' : 'AND ${contract.extensionType} != ?'}
      GROUP BY ${contract.id}
      ORDER BY ${contract.time}
      LIMIT ${context.cursor!.limit}
      OFFSET ${context.cursor!.offset}
    ''';

    return rawQuery(
      context,
      query,
      [
        ...DbQueryScopeUtils().queryArg(queryScope),
        day.dbValue,
        if (isGhostEntriesEnabled) ...[
          day.dbValue,
          day.dbValue,
          100,
          day.dbValue,
          ExtensionType.progresslog.dbValue,
          if (excludeProgresslog) ExtensionType.progresslog.dbValue,
        ],
      ],
    );
  }

  @override
  Future<List<Entry>> findAllInWeek(
    DbContext context,
    LogDay lastDayInWeek,
  ) async {
    final weekAgo = LogDay.fromDateTime(
        getDateTimeFromLogDay(lastDayInWeek).subtract(const Duration(days: 7)));
    final queryScope = context.queryScope!;
    var entryAlias = 'e';
    final query = '''
      SELECT ${columnsForSelect(context.fields!)} 
      ${DbQueryScopeUtils().accessFromJoin(queryScope, entryAlias: entryAlias)}
      WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
        AND ${contract.day} <= ?
        AND ${contract.day} >= ?
        AND ${contract.openStateId} IS NULL
        AND ${contract.extensionType} != ?
      GROUP BY ${contract.id}
      ORDER BY ${contract.time}
      LIMIT ${context.cursor!.limit}
      OFFSET ${context.cursor!.offset}
    ''';

    return rawQuery(
      context,
      query,
      [
        ...DbQueryScopeUtils().queryArg(queryScope),
        lastDayInWeek.dbValue,
        weekAgo.dbValue,
        ExtensionType.progresslog.dbValue
      ],
    );
  }

  @override
  Future<List<Entry>> findOpenEntries(
    DbContext context, {
    LocalId? assigneeId,
  }) async {
    final queryScope = context.queryScope!;
    final query = '''
      SELECT ${columnsForSelect(context.fields!)}
      ${DbQueryScopeUtils().accessFromJoin(queryScope)}
      LEFT JOIN ${openStateContract.tableName}
      ON ${contract.openStateId} = ${openStateContract.id}
      WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
      AND ${openStateContract.progress} < ?
      ${assigneeId == null ? '' : 'AND ${contract.assigneeId} = ?'}
      GROUP BY ${contract.id}
      ORDER BY ${contract.day}, ${openStateContract.endDay}, ${contract.time}
      LIMIT ${context.cursor!.limit}
      OFFSET ${context.cursor!.offset}
    ''';

    return rawQuery(
      context,
      query,
      [
        ...DbQueryScopeUtils().queryArg(queryScope),
        100,
        if (assigneeId != null) assigneeId.dbValue
      ],
    );
  }

  @override
  Future<LogDay?> findNextLogDay(
    DbContext context,
    LogDay day,
    int direction,
    bool isGhostEntriesEnabled,
  ) async {
    final queryScope = context.queryScope!;
    final nextDay = getLogDayAfter(day.value, direction);

    final closedQuery = '''
      SELECT ${contract.day} 
      ${DbQueryScopeUtils().accessFromJoin(queryScope)}
      WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
      AND ${contract.day} ${direction > 0 ? '>' : '<'} ?
      AND ${contract.openStateId} IS NULL
      ORDER BY ${contract.day} ${direction > 0 ? 'ASC' : 'DESC'}
      LIMIT 1
    ''';
    final closedList = await (await context.executor).rawQuery(
      closedQuery,
      [
        ...DbQueryScopeUtils().queryArg(queryScope),
        day.dbValue,
      ],
    );
    final closedDay =
        (closedList.isEmpty ? null : closedList[0][contract.day]) as int?;
    if (closedDay == nextDay) {
      return LogDay(nextDay);
    }

    if (!isGhostEntriesEnabled) {
      return closedDay == null ? null : LogDay(closedDay);
    }

    final openQuery = '''
      SELECT ${contract.day}, ${openStateContract.endDay} 
      ${DbQueryScopeUtils().accessFromJoin(queryScope)}
      LEFT JOIN ${openStateContract.tableName}
      ON ${contract.openStateId} = ${openStateContract.id}
      WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
      AND ${contract.openStateId} IS NOT NULL
      AND ${openStateContract.progress} != ?
      AND ${direction > 0 ? openStateContract.endDay : contract.day}
        ${direction > 0 ? '>' : '<'} ? 
      ORDER BY ${direction > 0 ? contract.day : openStateContract.endDay} 
        ${direction > 0 ? 'ASC' : 'DESC'}
    ''';
    final openList = await (await context.executor).rawQuery(openQuery, [
      ...DbQueryScopeUtils().queryArg(queryScope),
      100,
      day.dbValue,
    ]);

    if (openList.isEmpty) {
      return closedDay == null ? null : LogDay(closedDay);
    }

    if (direction > 0) {
      final openStartDay = openList[0][contract.day] as int;
      if (openStartDay <= nextDay) {
        return LogDay(nextDay);
      } else if (closedDay == null) {
        return LogDay(openStartDay);
      } else {
        return LogDay(min(closedDay, openStartDay));
      }
    } else {
      final openEndDay = openList[0][openStateContract.endDay] as int;
      if (openEndDay >= nextDay) {
        return LogDay(nextDay);
      } else if (closedDay == null) {
        return LogDay(openEndDay);
      } else {
        return LogDay(max(closedDay, openEndDay));
      }
    }
  }

  @override
  Future<List<Entry>> findByCreatedAt(
      DbContext context, EntryCreatedAt createdAt) async {
    // FIXME: createdAt loses precision in server round trip, need to fix:
    // where: '$etf_createdAt = ?',
    // whereArgs: [createdAt.dbValue],
    // Also, local createdAt has microsecond precision (...........)
    final time = createdAt.value;
    final min = EntryCreatedAt(time.subtract(const Duration(milliseconds: 2)));
    final max = EntryCreatedAt(time.add(const Duration(milliseconds: 2)));
    return query(
      context,
      where: '${contract.createdAt} >= ? AND ${contract.createdAt} <= ?',
      whereArgs: [min.dbValue, max.dbValue],
    );
  }

  @override
  Future<Entry?> findCheckInFromEntry(DbContext context, Entry entry) async {
    final personnellog = entry.personnellog!;
    final sqlQuery = '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM $tableName
      INNER JOIN ${personnellogContract.tableName}
      ON ${contract.extensionId} = ${personnellogContract.id}
      INNER JOIN ${projectEntriesProjectContract.tableName} 
      ON $idColumn = ${projectEntriesProjectContract.entryId}
      INNER JOIN ${projectContract.tableName}
      ON ${projectEntriesProjectContract.projectId} = ${projectContract.id}
      WHERE ${personnellogContract.name} = ?
      ${personnellog.project?.name?.dbValue == null ? '' : 'AND'
            ' ${projectContract.name} = ?'}
      ${personnellog.sublocation?.dbValue == null ? '' : 'AND'
            ' ${personnellogContract.sublocation} = ?'}
      AND ${personnellogContract.entrance} IS NOT NULL
      AND ${personnellogContract.exit} IS NULL
      AND ${personnellogContract.minutes} IS NULL
      AND (${contract.day} = ? OR ${contract.day} = ?)
    ''';

    return takeFirst(
      context.db.entry.rawQuery(context, sqlQuery, [
        personnellog.name!.dbValue,
        if (personnellog.project?.name?.dbValue != null)
          personnellog.project!.name!.dbValue,
        if (personnellog.sublocation?.dbValue != null)
          personnellog.sublocation!.dbValue!,
        getLogDayFromDateTime(DateTime.now()),
        getLogDayFromDateTime(DateTime.now().subtract(const Duration(days: 1))),
      ]),
    );
  }

  @override
  Future<List<Entry>> incompleteOpenEntries(DbContext context) async {
    final now = getLogDayFromDateTime(DateTime.now());
    final query = '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM $tableName
      LEFT JOIN ${openStateContract.tableName}
      ON ${contract.openStateId} = ${openStateContract.id}
      WHERE ${contract.openStateId} IS NOT NULL 
      AND ${contract.assigneeId} = ?
      AND ${openStateContract.endDay} >= ?
    ''';

    return context.db.entry.rawQuery(
      context,
      query,
      [context.queryScope!.userId!.dbValue, now],
    );
  }

  @override
  Future<Entry?> findWithStartedTimer(
    DbContext context, [
    LocalId? entryId,
  ]) async {
    return takeFirst(
      query(
        context,
        where: '${contract.timerStatus} = ? '
            '${entryId == null ? '' : 'AND $idColumn = ?'} ',
        whereArgs: [
          EntryTimerStatusValue.started.dbValue,
          if (entryId != null) entryId.dbValue
        ],
      ),
    );
  }

  @override
  Future<void> deleteProjectRelationIfNoOutgoingMutation(
      DbContext context, Project project) async {
    assert(!(project.isSyncable?.value ?? false));
    const outgoingMutationContract = OutgoingMutationDbContract();

    final entries = await (await context.executor).rawQuery(
      '''
      SELECT $idColumn from $tableName
        INNER JOIN ${projectEntriesProjectContract.tableName}
        ON $idColumn = ${projectEntriesProjectContract.entryId}
        LEFT JOIN ${outgoingMutationContract.tableName}
        ON $idColumn = ${outgoingMutationContract.modelId}
        WHERE ${projectEntriesProjectContract.projectId} = ? 
        AND ${outgoingMutationContract.modelId} IS NULL 
      ''',
      [project.id!.dbValue],
    );

    for (final entry in entries) {
      await _doDeleteProjectRelation(
        context,
        Entry(id: LocalId(entry[contract.id] as int)),
        project,
      );
    }
  }

  @override
  Future<void> deleteProjectRelation(
      DbContext context, Entry entry, Project project) async {
    return wrapTransaction(context, (wrappedContext) async {
      await _doDeleteProjectRelation(wrappedContext, entry, project);
    });
  }

  @override
  Future<void> cleanUp(DbContext context) async {
    await _cleanupOrphanEntries(context);
    await _cleanupLooseExtensions(context);
    await _cleanupLooseUsers(context);
  }

  @override
  Future<void> setAccessCache(DbContext context, Iterable<LocalId> ids) async {
    if (ids.length == 1) {
      await _setAccessCache(context, ids.first);
      return;
    }

    final batch = (await context.executor).batch();

    for (final id in ids) {
      await _setAccessCache(context, id, batch);
    }

    await batch.commit();
  }

  Future<void> _setAccessCache(
    DbContext context,
    LocalId id, [
    Batch? batch,
  ]) async {
    final permission = await getPermissionForEntry(context, id);
    if (permission == null) {
      return;
    }

    final accessCache = {
      accessEntryContract.entryId: id.dbValue,
      accessEntryContract.permission: permission,
    };

    if (batch == null) {
      final executor = await context.executor;
      await executor.insert(
        accessEntryContract.tableName,
        accessCache,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return;
    }

    batch.insert(
      accessEntryContract.tableName,
      accessCache,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> _doDeleteProjectRelation(
      DbContext context, Entry entry, Project project) async {
    if (entry.id == null) {
      final entryWithId = await context.db.query(
        EntryIdRepositoryQuery(remoteId: entry.remoteId!),
        context: context,
      );
      if (entryWithId == null) {
        return;
      }
      return _doDeleteProjectRelation(context, entryWithId, project);
    }

    if (project.id == null) {
      final projectWithId = await context.db
          .query(ProjectIdRepositoryQuery(remoteId: project.remoteId!));
      if (projectWithId == null) {
        return;
      }
      return _doDeleteProjectRelation(context, entry, projectWithId);
    }

    final executor = await (context.executor);
    await executor.delete(projectEntriesProjectContract.tableName,
        where: '${projectEntriesProjectContract.entryId} = ?'
            ' AND ${projectEntriesProjectContract.projectId} = ?',
        whereArgs: [entry.id!.dbValue, project.id!.dbValue]);

    final otherRelations = await executor.query(
      projectEntriesProjectContract.tableName,
      where: '${projectEntriesProjectContract.entryId} = ?',
      whereArgs: [entry.id!.dbValue],
    );

    if (otherRelations.isEmpty) {
      await delete(context, entry.id!);
    } else {
      broadcastMutation(Mutation(
        type: MutationType.update,
        id: entry.id!,
        model: entry,
      ));
    }
  }

  @override
  Future<void> onSaved(DbContext context, Mutation<Entry> mutation) async {
    final model = mutation.model!;

    if (model.attachments != null) {
      await context.db.attachment.saveAll(context, mutation);
    }
    if (model.tags != null) {
      await context.db.tag.saveAll(context, mutation);
    }
    if (model.source != null) {
      await context.db.entrySource.save(
        context,
        model.source!.copyWith(entry: Entry(id: mutation.id)),
      );
    }
    if (model.metadata != null) {
      await context.db.entryMetadata.saveAll(context, mutation);
    }
    if (mutation.type == MutationType.update && model.signatures != null) {
      await context.db.signature.saveAll(context, mutation);
    }

    await _fixEntryRelationsAfterSave(context, mutation);
    return super.onSaved(context, mutation);
  }

  @override
  Future<void> onSyncRequested(
      DbContext context, Mutation<Entry> mutation) async {
    final entry = await context.db.query(
      EntryForOutgoingMutationQuery(id: mutation.id!),
      context: context,
    );

    if (mutation.type == MutationType.delete &&
        entry?.remoteId?.value == null) {
      return;
    }

    if (entry?.entryDraft != null) {
      logger.w('db:entry Tried to sync entry with draft. Ignoring.');
      return;
    }

    if (entry == null) {
      // FIXME: need to investigate why we sometimes get null entry
      // Hard to repro. Insert (3) and immediately spam delete
      // To repro with attachments:
      // 1. Delay attachment upload (after actual upload starts)
      // 2. Delete entry immediately (before attachment upload ends).
      logger.f('db:entry Tried to sync missing entry.');
      return;
    }

    await context.db.outgoingMutation.save(
      context,
      OutgoingMutation(
        key: OutgoingMutationKey(DateTime.now().microsecondsSinceEpoch),
        mutationType: mutation.type,
        model: OutgoingMutationModel(
          entry,
          OutgoingMutationModelType.fromExtensionType(
            entry.extension!.extensionType,
          ),
        ),
        organization: entry.projects.first.organization,
      ),
    );
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    assert(context.txn != null);
    logger.i('db:entry Pre-deleting $id');

    final executor = await context.executor;

    final result = await executor.query(
      tableName,
      columns: [
        contract.extensionId,
        contract.extensionType,
        contract.openStateId,
        contract.locationTrackingId,
      ],
      where: '$idColumn = ?',
      whereArgs: [id.dbValue],
    );

    if (result.isEmpty) {
      return;
    }
    if (result.length > 1) {
      throw 'Unexpected number of children to delete';
    }

    final extensionId = LocalId(result[0][contract.extensionId] as int);
    final extensionType =
        ExtensionType.fromDbValue(result[0][contract.extensionType] as int);
    if (extensionType == ExtensionType.templatelog) {
      await context.db.customFieldMetadata.deleteAll(context, extensionId);
    }
    await _deleteExtension(
      context,
      extensionId,
      extensionType,
    );

    final openStateId = result[0][contract.openStateId] as int?;
    if (openStateId != null) {
      await context.db.openState.delete(context, LocalId(openStateId));
    }

    final childrenEntryIds = await _fetchChildEntryIds(id, executor);
    if (childrenEntryIds.isNotEmpty) {
      for (final childEntryId in childrenEntryIds) {
        await delete(context, childEntryId);
      }
    }

    // Project-entry-project.
    await executor.delete(
      projectEntriesProjectContract.tableName,
      where: '${projectEntriesProjectContract.entryId} = ?',
      whereArgs: [id.dbValue],
    );

    // Access-entry
    await executor.delete(accessEntryContract.tableName,
        where: '${accessEntryContract.entryId} = ?', whereArgs: [id.dbValue]);

    // Mutations with modelId are insert or update. Deleting those.
    const outgoingMutationContract = OutgoingMutationDbContract();
    await executor.delete(outgoingMutationContract.tableName,
        where: '${outgoingMutationContract.modelId} = ? '
            'AND ${outgoingMutationContract.modelType} = ?',
        whereArgs: [
          id.dbValue,
          OutgoingMutationModelType.fromExtensionType(extensionType).dbValue,
        ]);

    await context.db.attachment.deleteAll(context, id);
    await context.db.tag.deleteAll(context, id);
    await context.db.signature.deleteAll(context, id);
    await context.db.entryGroupEntry.deleteAll(context, entryId: id);
    await context.db.entryMetadata.deleteAll(context, id);

    if (result[0][contract.locationTrackingId] != null) {
      await context.db.locationTracking.delete(
        context,
        LocalId(result[0][contract.locationTrackingId] as int),
      );
    }
  }

  Future<void> _deleteExtension(
    DbContext context,
    LocalId extensionId,
    ExtensionType extensionType,
  ) async {
    final db = context.db;
    switch (extensionType) {
      case ExtensionType.worklog:
      case ExtensionType.simplelog:
        await db.worklog.delete(context, extensionId);
        break;
      case ExtensionType.inventorylog:
        await db.inventorylog.delete(context, extensionId);
        break;
      case ExtensionType.personnellog:
        await db.personnellog.delete(context, extensionId);
        break;
      case ExtensionType.progresslog:
        await db.progresslog.delete(context, extensionId);
        break;
      case ExtensionType.templatelog:
        await db.templatelog.delete(context, extensionId);
        break;
    }
  }

  Future<void> _fixEntryRelationsAfterSave(
      DbContext context, Mutation<Entry> entryMutation) async {
    final parentId = entryMutation.id!;
    final entry = await context.db
        .query(EntryRelationsDbQuery(parentId), context: context);
    if (entry == null) {
      logger.f('No entry found after save.');
      return;
    }

    final executor = await context.executor;

    final ids = [parentId];
    if (entry.openState != null) {
      final childIds = await _fetchChildEntryIds(parentId, executor);
      ids.addAll(childIds);
    }

    for (final id in ids) {
      // Project-entries-project
      if (entryMutation.type != MutationType.insert) {
        await executor.delete(
          projectEntriesProjectContract.tableName,
          where: '${projectEntriesProjectContract.entryId} = ?',
          whereArgs: [id.dbValue],
        );
      }

      for (final project in entry.projects) {
        await context.db.project
            .saveEntryRelation(context, project, Entry(id: id));
      }

      // Access-entry
      await setAccessCache(context, [id]);
    }
  }

  Future<void> _cleanupOrphanEntries(DbContext context) async {
    final executor = await context.executor;
    final result = await executor.rawQuery('''
        SELECT $idColumn from $tableName
        LEFT JOIN ${projectEntriesProjectContract.tableName}
        ON $idColumn = ${projectEntriesProjectContract.entryId}
        LEFT JOIN ${projectContract.tableName}
        ON ${projectEntriesProjectContract.projectId} = ${projectContract.id}
        WHERE ${projectContract.id} IS NULL
      ''');
    final idsToDelete = result.map<LocalId>((e) => LocalId(e[idColumn] as int));
    await Future.forEach<LocalId>(idsToDelete, (e) => delete(context, e));
  }

  Future<void> _cleanupLooseExtensions(DbContext context) async {
    for (final extensionType in ExtensionType.values) {
      if (extensionType == ExtensionType.simplelog) {
        continue;
      }

      final extensionIdColumn = extensionType.idColumn;
      final extensionTableName = extensionType.tableName;
      final executor = await context.executor;
      await executor.rawDelete('''
        DELETE FROM $extensionTableName WHERE $extensionIdColumn IN (
          SELECT $extensionIdColumn FROM $extensionTableName
          LEFT JOIN ${contract.tableName} ON (
            ${contract.extensionType} = ? 
            AND ${contract.extensionId} = $extensionIdColumn
          )
          WHERE ${contract.id} IS NULL 
        )
      ''', [extensionType.dbValue]);
    }
  }

  /// Assumes no other reason for users in user table other than assignees
  /// in entries.
  Future<void> _cleanupLooseUsers(DbContext context) async {
    final executor = await context.executor;
    await executor.rawDelete('''
        DELETE FROM ${userContract.tableName} WHERE ${userContract.id} IN (
          SELECT ${userContract.id} FROM ${userContract.tableName}
          LEFT JOIN ${contract.tableName}
          ON (${contract.assigneeId} = ${userContract.id})
          WHERE ${contract.id} IS NULL
        ) AND ${userContract.remoteId} IS NULL
      ''');
  }

  Future<List<LocalId>> _fetchChildEntryIds(
      LocalId id, DatabaseExecutor executor) async {
    final result = await executor.rawQuery(
      '''
          SELECT ${contract.id} FROM ${contract.tableName}
          LEFT JOIN ${progresslogContract.tableName}
          ON ${contract.extensionId} = ${progresslogContract.id}
          WHERE ${contract.extensionType} = ? 
          AND ${progresslogContract.entryId} = ?
        ''',
      [ExtensionType.progresslog.dbValue, id.dbValue],
    );
    return result
        .map<LocalId>((e) => LocalId(e[contract.id] as int))
        .toList(growable: false);
  }

  @override
  Future<List<String>> assignees(DbContext context) async {
    assert(context.queryScope!.byOrg);

    final limit = context.cursor?.limit ?? 10;
    final executor = await context.executor;

    final resultsFromEntries = await executor.rawQuery(
      '''
        SELECT ${userContract.id}, ${userContract.name}, ${userContract.email}, 
        MAX(${contract.updatedAt})
        ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
        LEFT JOIN ${userContract.tableName}
        ON ${contract.assigneeId} = ${userContract.id}
        WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
        AND ${_getPatternMatchQueryCondition(context.queryScope!.pattern!)}
        GROUP BY ${userContract.name}, ${userContract.email}
        ORDER BY MAX(${contract.updatedAt}) DESC, ${userContract.name} ASC
        LIMIT $limit
      ''',
      [
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        ..._getPatternMatchQueryArguments(context.queryScope!.pattern!),
      ],
    );

    const userOrganizationContract = UserOrganizationDbContract();

    final resultsFromOrgUsers = await executor.rawQuery(
      '''
        SELECT ${userContract.name}, ${userContract.email}
        FROM ${userContract.tableName}
        LEFT JOIN ${userOrganizationContract.tableName}
        ON ${userContract.id} = ${userOrganizationContract.userId}
        WHERE ${userOrganizationContract.organizationId} = ?
        AND ${_getPatternMatchQueryCondition(context.queryScope!.pattern!)}
        AND ${userContract.id}
        NOT IN (${resultsFromEntries.map((e) => '?').join(',')}) 
        ORDER BY ${userContract.name} ASC
        LIMIT ${limit - resultsFromEntries.length}
      ''',
      [
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        ..._getPatternMatchQueryArguments(context.queryScope!.pattern!),
        ...resultsFromEntries.map((e) => e[userContract.id]),
      ],
    );

    return [...resultsFromEntries, ...resultsFromOrgUsers]
        .map<String>((e) => User(
              name: UserName(e[userContract.name] as String),
              email: UserEmail(e[userContract.email] as String?),
            ).nameEmailDisplayValue)
        .toList(growable: false);
  }

  @override
  Future<Map<LogDay, int>> getEntryCountByMonth(
    DbContext context,
    DateTime date,
  ) async {
    final daysOfMonth = DateTime(date.year, date.month, 0).day;
    final initLogDay = LogDay.fromDateTime(DateTime(date.year, date.month, 1));
    final endLogDay =
        LogDay.fromDateTime(DateTime(date.year, date.month, daysOfMonth));

    return _getEntryCountByRange(context, initLogDay, endLogDay);
  }

  @override
  Future<Map<LogDay, int>> getEntryCountByWeek(
    DbContext context,
    DateTime date,
  ) async {
    final weekDay = date.weekday;
    final initLogDay =
        LogDay.fromDateTime(date.subtract(Duration(days: weekDay - 1)));
    final endLogDay = LogDay.fromDateTime(
        date.add(Duration(days: DateTime.daysPerWeek - date.weekday)));

    return _getEntryCountByRange(context, initLogDay, endLogDay);
  }

  Future<Map<LogDay, int>> _getEntryCountByRange(
    DbContext context,
    LogDay init,
    LogDay end,
  ) async {
    final queryScope = context.queryScope;
    final executor = await context.executor;
    final result = await executor.rawQuery(
      '''
        SELECT ${contract.day}, COUNT(${contract.id}) AS count
        ${DbQueryScopeUtils().accessFromJoin(queryScope!)}
        WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
        AND ${contract.day} >= ? 
        AND ${contract.day} <= ?
        AND ${contract.openStateId} IS NULL
        GROUP BY ${contract.day}
      ''',
      [
        ...DbQueryScopeUtils().queryArg(queryScope),
        init.dbValue,
        end.dbValue,
      ],
    );

    final count = <LogDay, int>{};
    for (int i = init.value; i <= end.value; i++) {
      final dayCount =
          result.firstWhereOrNull((e) => e['e_day'] == i)?['count'] as int?;

      count.addAll({LogDay(i): dayCount ?? 0});
    }
    return count;
  }

  String _getPatternMatchQueryCondition(String pattern) {
    final matchedUser = User.fromNameEmailDisplayValue(pattern);
    return matchedUser != null
        ? '((${userContract.name} = ? AND ${userContract.email} = ?)'
            ' OR ${userContract.name} = ?)'
        : '(${userContract.name} LIKE ? OR ${userContract.email} LIKE ?)';
  }

  List<String> _getPatternMatchQueryArguments(String pattern) {
    final matchedUser = User.fromNameEmailDisplayValue(pattern);
    return matchedUser != null
        ? [
            matchedUser.name!.displayValue,
            matchedUser.email!.displayValue,
            pattern,
          ]
        : [pattern, pattern];
  }

  @override
  Future<Entry?> findByLocationTrackingId(
    DbContext context,
    LocalId locationTrackingId,
  ) {
    return takeFirst(
      query(
        context,
        where: '${contract.locationTrackingId} = ?',
        whereArgs: [locationTrackingId.dbValue],
      ),
    );
  }

  @override
  Future<Entry?> findByExtension(
    DbContext context,
    ExtensionType extensionType,
    LocalId extensionId,
  ) {
    return takeFirst(
      query(
        context,
        where: '${contract.extensionType} = ? AND ${contract.extensionId} = ?',
        whereArgs: [extensionType.dbValue, extensionId.dbValue],
      ),
    );
  }

  @override
  Future<List<Entry>> findByEntryGroup(
      DbContext context, LocalId entryGroupId) {
    const entryGroupEntry = EntryGroupEntryDbContract();
    const entryGroup = EntryGroupDbContract();
    return rawQuery(
      context,
      '''
        SELECT ${columnsForSelect(context.fields!)} FROM $tableName
        LEFT JOIN ${entryGroupEntry.tableName}
          ON ${contract.id} = ${entryGroupEntry.entryId}
        LEFT JOIN ${entryGroup.tableName}
          ON ${entryGroupEntry.entryGroupId} = ${entryGroup.id}
        WHERE ${entryGroup.id} = ?
        ORDER BY ${contract.day} ASC
      ''',
      [entryGroupId.dbValue],
    );
  }

  @override
  Future<List<Entry>> searchAll(
    DbContext context,
    String pattern,
    EntryFilter filter,
  ) async {
    final queryScope = context.queryScope!;

    const tagEntryContract = TagEntryDbContract();
    const tagContract = TagDbContract();
    final columns = columnsForSelect(context.fields!);

    final entrySearchableFields = EntryExtensionSearchableFields(context.db);
    final searchColumns =
        entrySearchableFields.extensionColumns(context.fields!);

    final projectIds = filter.projects.map((e) => e.id!);
    final projectCondition = projectIds.isEmpty
        ? ''
        : '${projectContract.id} IN '
            '(${projectIds.map((e) => '?').join(',')})';

    final tagIds = filter.tags.map((e) => e.id!);
    final tagCondition = tagIds.isEmpty
        ? ''
        : '${tagContract.id} IN (${tagIds.map((e) => '?').join(',')})';

    final authorIds = filter.authors.map((e) => e.id!);
    final authorCondition = authorIds.isEmpty
        ? ''
        : '${contract.authorId} IN '
            '(${authorIds.map((e) => '?').join(',')})';

    final searchCondition = pattern.isEmpty ? '' : '($searchColumns LIKE ?)';

    final whereConditions = [
      if (projectCondition.isNotEmpty) projectCondition,
      if (tagCondition.isNotEmpty) tagCondition,
      if (authorCondition.isNotEmpty) authorCondition,
      if (searchCondition.isNotEmpty) searchCondition,
    ];

    return rawQuery(
      context,
      '''
        SELECT $columns
        ${DbQueryScopeUtils().accessFromJoin(queryScope)} 
        ${entrySearchableFields.extensionJoin()}
        LEFT JOIN ${tagEntryContract.tableName}
        ON ${contract.id} = ${tagEntryContract.entryId}
        LEFT JOIN ${tagContract.tableName}
        ON ${tagEntryContract.tagId} = ${tagContract.id}
        WHERE ${DbQueryScopeUtils().accessWhere(queryScope)}
        ${whereConditions.isEmpty ? '' : 'AND ${whereConditions.join(' AND ')}'}
        GROUP BY ${contract.id}
        ORDER BY ${contract.day} DESC
        LIMIT ${context.cursor!.limit}
        OFFSET ${context.cursor!.offset}
        ''',
      [
        ...DbQueryScopeUtils().queryArg(queryScope),
        if (projectIds.isNotEmpty) ...projectIds.map((e) => e.dbValue),
        if (tagIds.isNotEmpty) ...tagIds.map((e) => e.dbValue),
        if (authorIds.isNotEmpty) ...authorIds.map((e) => e.dbValue),
        if (pattern.isNotEmpty) '%$pattern%',
      ],
    );
  }
}
