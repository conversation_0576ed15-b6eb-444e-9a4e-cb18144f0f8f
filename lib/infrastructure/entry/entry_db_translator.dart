import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';

class EntryDbTranslator implements DbTranslator<Entry> {
  const EntryDbTranslator();

  @override
  Set<Field> get nestedModelFields => entryNestedModelFields;

  @override
  Future<Entry> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    final day = fields[EntryField.day]?.value(map);
    return Entry(
      id: fields[EntryField.id]?.value(map),
      remoteId: fields[EntryField.remoteId]?.value(map),
      day: day,
      time: fields[EntryField.time]?.value(map),
      startDate: fields[EntryField.startDate]?.value(map),
      endDate: fields[EntryField.endDate]?.value(map),
      startTime: fields[EntryField.startTime]?.value(map),
      endTime: fields[EntryField.endTime]?.value(map),
      comments: fields[EntryField.comments]?.value(map),
      location: fields[EntryField.location]?.value(map),
      createdAt: fields[EntryField.createdAt]?.value(map),
      updatedAt: fields[EntryField.updatedAt]?.value(map),
      syncVersion: fields[EntryField.syncVersion]?.value(map),
      timerStatus: fields[EntryField.timerStatus]?.value(map),
      assignee: await fields[EntryField.assignee]?.nested(context, map),
      author: await fields[EntryField.author]?.nested(context, map),
      extension: await fields[EntryField.extension]?.nested(context, map),
      openState: await fields[EntryField.openState]?.nested(context, map,
          props: day != null ? {'startDay': day.value} : null),
      entryGroupEntry:
          await fields[EntryField.entryGroupEntry]?.nested(context, map),
      attachments: await fields[EntryField.attachments]?.nested(context, map),
      tags: await fields[EntryField.tags]?.nested(context, map),
      locationTracking:
          await fields[EntryField.locationTracking]?.nested(context, map),
      signatures: await fields[EntryField.signatures]?.nested(context, map),
      source: await fields[EntryField.source]?.nested(context, map),
      metadata: await fields[EntryField.metadata]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Entry model) async {
    const syncVersion = EntrySyncVersion(kEntrySyncVersion);

    final map = <String, dynamic>{};
    const contract = EntryDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.day, model.day);
    addField(map, contract.time, model.time);
    addField(map, contract.startDate, model.startDate);
    addField(map, contract.endDate, model.endDate);
    addField(map, contract.startTime, model.startTime);
    addField(map, contract.endTime, model.endTime);
    addField(map, contract.comments, model.comments);
    addField(map, contract.createdAt, model.createdAt);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.syncVersion, syncVersion);
    addField(map, contract.timerStatus, model.timerStatus);

    if (model.location != null) {
      map[contract.locLongitude] = model.location!.value?.longitude;
      map[contract.locLatitude] = model.location!.value?.latitude;
    }

    if (model.extension != null) {
      map[contract.extensionType] = model.extension!.extensionType.dbValue;
      await saveNestedModel<Extension>(context, map, contract.extensionId,
          _getExtensionRepository(context, model.extension!), model.extension);
    }

    await saveNestedModel<User>(
        context, map, contract.assigneeId, context.db.user, model.assignee);
    await saveNestedModel<User>(
        context, map, contract.authorId, context.db.user, model.author);

    await saveNestedModel<OpenState>(context, map, contract.openStateId,
        context.db.openState, model.openState);

    await saveNestedModel<LocationTracking>(
      context,
      map,
      contract.locationTrackingId,
      context.db.locationTracking,
      model.locationTracking,
    );

    /// FIXME: save nested attachments and tags here? Not possible (id required)
    return map;
  }

  DbTable<Extension, dynamic> _getExtensionRepository(
    DbContext context,
    Extension extension,
  ) {
    switch (extension.extensionType) {
      case ExtensionType.worklog:
        return context.db.worklog;
      case ExtensionType.inventorylog:
        return context.db.inventorylog;
      case ExtensionType.personnellog:
        return context.db.personnellog;
      case ExtensionType.progresslog:
        return context.db.progresslog;
      case ExtensionType.templatelog:
        return context.db.templatelog;
      default:
        throw Exception('Unexpected extension type');
    }
  }
}
