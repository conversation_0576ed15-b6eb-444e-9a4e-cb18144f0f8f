import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/address/address.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/avatar/avatar.dart';
import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/email/email.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/feature_flag/feature_flag.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/phone/phone.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/domain/report_template/report_template.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource_category/resource_category.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/domain/template_condition/template_condition.dart';
import 'package:bitacora/domain/template_group/template_group.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/infrastructure/access/access_api_translator.dart';
import 'package:bitacora/infrastructure/address/address_api_translator.dart';
import 'package:bitacora/infrastructure/attachment/attachment_api_translator.dart';
import 'package:bitacora/infrastructure/avatar/avatar_api_translator.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_api_translator.dart';
import 'package:bitacora/infrastructure/custom_field_allowed_value/custom_field_allowed_value_api_translator.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_api_translator.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_api_translator.dart';
import 'package:bitacora/infrastructure/email/email_api_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_api_translator.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_api_translator.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_api_translator.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_api_translator.dart';
import 'package:bitacora/infrastructure/feature_flag/feature_flag_api_translator.dart';
import 'package:bitacora/infrastructure/feed_post/feed_post_api_translator.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_api_translator.dart';
import 'package:bitacora/infrastructure/location_point/location_point_api_translator.dart';
import 'package:bitacora/infrastructure/location_tracking/location_tracking_api_translator.dart';
import 'package:bitacora/infrastructure/open_state/open_state_api_translator.dart';
import 'package:bitacora/infrastructure/organization/organization_api_translator.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_api_translator.dart';
import 'package:bitacora/infrastructure/person/person_api_translator.dart';
import 'package:bitacora/infrastructure/person_detail/person_detail_api_translator.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_api_translator.dart';
import 'package:bitacora/infrastructure/phone/phone_api_translator.dart';
import 'package:bitacora/infrastructure/product/product_api_translator.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_api_translator.dart';
import 'package:bitacora/infrastructure/project/project_api_translator.dart';
import 'package:bitacora/infrastructure/qr_code/qr_code_api_translator.dart';
import 'package:bitacora/infrastructure/report/report_api_translator.dart';
import 'package:bitacora/infrastructure/report_template/report_template_api_translator.dart';
import 'package:bitacora/infrastructure/resource/resource_api_translator.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_api_translator.dart';
import 'package:bitacora/infrastructure/signature/signature_api_translator.dart';
import 'package:bitacora/infrastructure/tag/tag_api_translator.dart';
import 'package:bitacora/infrastructure/template/template_api_translator.dart';
import 'package:bitacora/infrastructure/template_block/template_block_api_translator.dart';
import 'package:bitacora/infrastructure/template_condition/template_condition_api_translator.dart';
import 'package:bitacora/infrastructure/template_group/template_group_api_translator.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_api_translator.dart';
import 'package:bitacora/infrastructure/user/user_api_translator.dart';
import 'package:bitacora/infrastructure/worklog/worklog_api_translator.dart';
import 'package:bitacora/util/inject/inject.dart';

class ApiTranslator implements Translator {
  factory ApiTranslator() => inject(() => const ApiTranslator._());

  const ApiTranslator._();

  @override
  ModelTranslator<Access> get access => const AccessApiTranslator();

  @override
  ModelTranslator<Address> get address => const AddressApiTranslator();

  @override
  ModelTranslator<Attachment> get attachment => const AttachmentApiTranslator();

  @override
  ModelTranslator<Avatar> get avatar => const AvatarApiTranslator();

  @override
  ModelTranslator<CustomField> get customField =>
      const CustomFieldApiTranslator();

  @override
  ModelTranslator<CustomFieldMetadata> get customFieldMetadata =>
      const CustomFieldMetadataApiTranslator();

  @override
  ModelTranslator<CustomFieldOptions> get customFieldOptions =>
      const CustomFieldOptionsApiTranslator();

  @override
  ModelTranslator<CustomFieldAllowedValue> get customFieldAllowedValue =>
      const CustomFieldAllowedValueApiTranslator();

  @override
  ModelTranslator<Entry> get entry => EntryApiTranslator(ApiTranslator());

  @override
  ModelTranslator<Email> get email => const EmailApiTranslator();

  @override
  ModelTranslator<EntryGroup> get entryGroup =>
      EntryGroupApiTranslator(ApiTranslator());

  @override
  ModelTranslator<EntryGroupEntry> get entryGroupEntry =>
      const EntryGroupEntryApiTranslator();

  @override
  ModelTranslator<EntryMetadata> get entryMetadata =>
      EntryMetadataApiTranslator();

  @override
  ModelTranslator<FeatureFlag> get featureFlag =>
      const FeatureFlagApiTranslator();

  @override
  ModelTranslator<FeedPost> get feedPost =>
      FeedPostApiTranslator(ApiTranslator());

  @override
  ModelTranslator<Inventorylog> get inventorylog =>
      const InventorylogApiTranslator();

  @override
  ModelTranslator<OpenState> get openState => const OpenStateApiTranslator();

  @override
  ModelTranslator<Organization> get organization =>
      const OrganizationApiTranslator();

  @override
  ModelTranslator<OutgoingMutation> get outgoingMutation =>
      OutgoingMutationApiTranslator(ApiTranslator());

  @override
  ModelTranslator<Person> get person => const PersonApiTranslator();

  @override
  ModelTranslator<PersonDetail> get personDetail =>
      const PersonDetailApiTranslator();

  @override
  ModelTranslator<Personnellog> get personnellog =>
      const PersonnellogApiTranslator();

  @override
  ModelTranslator<Phone> get phone => const PhoneApiTranslator();

  @override
  ModelTranslator<Product> get product => const ProductApiTranslator();

  @override
  ModelTranslator<Progresslog> get progresslog =>
      const ProgresslogApiTranslator();

  @override
  ModelTranslator<Templatelog> get templatelog =>
      TemplatelogApiTranslator(this);

  @override
  ModelTranslator<Project> get project => const ProjectApiTranslator();

  @override
  ModelTranslator<QrCode> get qrCode => const QrCodeApiTranslator();

  @override
  ModelTranslator<Report> get report => const ReportApiTranslator();

  @override
  ModelTranslator<Resource> get resource => const ResourceApiTranslator();

  @override
  ModelTranslator<ResourceCategory> get resourceCategory =>
      const ResourceCategoryApiTranslator();

  @override
  ModelTranslator<ReportTemplate> get reportTemplate =>
      const ReportTemplateApiTranslator();

  @override
  ModelTranslator<Signature> get signature => const SignatureApiTranslator();

  @override
  ModelTranslator<Tag> get tag => const TagApiTranslator();

  @override
  ModelTranslator<Template> get template => const TemplateApiTranslator();

  @override
  ModelTranslator<TemplateBlock> get templateBlock =>
      const TemplateBlockApiTranslator();

  @override
  ModelTranslator<TemplateCondition> get templateCondition =>
      const TemplateConditionApiTranslator();

  @override
  ModelTranslator<TemplateGroup> get templateGroup =>
      const TemplateGroupApiTranslator();

  @override
  ModelTranslator<User> get user => const UserApiTranslator();

  @override
  ModelTranslator<Worklog> get worklog => const WorklogApiTranslator();

  @override
  ModelTranslator<LocationTracking> get locationTracking =>
      const LocationTrackingApiTranslator();

  @override
  ModelTranslator<LocationPoint> get locationPoint =>
      const LocationPointApiTranslator();
}
