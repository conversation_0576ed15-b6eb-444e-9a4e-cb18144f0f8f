import 'dart:async';

import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/infrastructure/access/access_db_contract.dart';
import 'package:bitacora/infrastructure/access/access_db_table.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:bitacora/infrastructure/address/address_db_contract.dart';
import 'package:bitacora/infrastructure/address/address_db_table.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_contract.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_table.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_contract.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_table.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_table.dart';
import 'package:bitacora/infrastructure/custom_field_allowed_value/custom_field_allowed_value_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_allowed_value/custom_field_allowed_value_db_table.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_table.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_table.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/infrastructure/email/email_db_contract.dart';
import 'package:bitacora/infrastructure/email/email_db_table.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry/entry_db_table.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_contract.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_table.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_table.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_table.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_table.dart';
import 'package:bitacora/infrastructure/entry_source/entry_source_db_contract.dart';
import 'package:bitacora/infrastructure/entry_source/entry_source_db_table.dart';
import 'package:bitacora/infrastructure/feature_flag/feature_flag_db_contract.dart';
import 'package:bitacora/infrastructure/feature_flag/feature_flag_db_table.dart';
import 'package:bitacora/infrastructure/feed_post/feed_post_db_contract.dart';
import 'package:bitacora/infrastructure/feed_post/feed_post_db_table.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_table.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_contract.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_table.dart';
import 'package:bitacora/infrastructure/location_tracking/location_tracking_db_contract.dart';
import 'package:bitacora/infrastructure/location_tracking/location_tracking_db_table.dart';
import 'package:bitacora/infrastructure/logging_executor.dart';
import 'package:bitacora/infrastructure/open_state/open_state_db_contract.dart';
import 'package:bitacora/infrastructure/open_state/open_state_db_table.dart';
import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';
import 'package:bitacora/infrastructure/organization/organization_db_table.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_contract.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_table.dart';
import 'package:bitacora/infrastructure/person/person_db_contract.dart';
import 'package:bitacora/infrastructure/person/person_db_table.dart';
import 'package:bitacora/infrastructure/person_detail/person_detail_db_contract.dart';
import 'package:bitacora/infrastructure/person_detail/person_detail_db_table.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_table.dart';
import 'package:bitacora/infrastructure/phone/phone_db_contract.dart';
import 'package:bitacora/infrastructure/phone/phone_db_table.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_table.dart';
import 'package:bitacora/infrastructure/product/product_db_contract.dart';
import 'package:bitacora/infrastructure/product/product_db_table.dart';
import 'package:bitacora/infrastructure/product_user/product_user_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_table.dart';
import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:bitacora/infrastructure/qr_code/qr_code_db_contract.dart';
import 'package:bitacora/infrastructure/qr_code/qr_code_db_table.dart';
import 'package:bitacora/infrastructure/report/report_db_contract.dart';
import 'package:bitacora/infrastructure/report/report_db_table.dart';
import 'package:bitacora/infrastructure/report_template/report_template_db_contract.dart';
import 'package:bitacora/infrastructure/report_template/report_template_db_table.dart';
import 'package:bitacora/infrastructure/resource/resource_aggregation_db_contract.dart';
import 'package:bitacora/infrastructure/resource/resource_db_contract.dart';
import 'package:bitacora/infrastructure/resource/resource_db_table.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_contract.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_table.dart';
import 'package:bitacora/infrastructure/resource_category/resource_catergory_resource_db_contract.dart';
import 'package:bitacora/infrastructure/signature/signature_db_contract.dart';
import 'package:bitacora/infrastructure/signature/signature_db_table.dart';
import 'package:bitacora/infrastructure/sqflite_utils.dart';
import 'package:bitacora/infrastructure/sync_metadata/sync_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/sync_metadata/sync_metadata_db_table.dart';
import 'package:bitacora/infrastructure/tag/tag_db_contract.dart';
import 'package:bitacora/infrastructure/tag/tag_db_table.dart';
import 'package:bitacora/infrastructure/tag/tag_entry_db_contract.dart';
import 'package:bitacora/infrastructure/template/template_db_contract.dart';
import 'package:bitacora/infrastructure/template/template_db_table.dart';
import 'package:bitacora/infrastructure/template_block/template_block_db_contract.dart';
import 'package:bitacora/infrastructure/template_block/template_block_db_table.dart';
import 'package:bitacora/infrastructure/template_condition/template_condition_db_contract.dart';
import 'package:bitacora/infrastructure/template_condition/template_condition_db_table.dart';
import 'package:bitacora/infrastructure/template_group/template_group_db_contract.dart';
import 'package:bitacora/infrastructure/template_group/template_group_db_table.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_table.dart';
import 'package:bitacora/infrastructure/user/user_db_contract.dart';
import 'package:bitacora/infrastructure/user/user_db_table.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_contract.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_table.dart';
import 'package:bitacora/infrastructure/user_location_tracking/user_location_tracking_db_contract.dart';
import 'package:bitacora/infrastructure/user_organization/user_organization_db_contract.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_table.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/platform_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

const kDbVersion = 1;
const kDbVersionWithAttachmentTransferAttempt = 2;
const kDbVersionWithUserInviteTable = 3;
const kDBVersionWithEntrySyncVersion = 4;
const kDbVersionWithEntryDateTimeRange = 5;
const kDbVersionWithReportTable = 6;
const kDbVersionWithReportTemplateTable = 7;
const kDbVersionWithOrganizationActivePlan = 8;
const kDbVersionWithReportParams = 9;
const kDbVersionWithOutgoingMutationFailedAttempts = 10;
const kDbVersionWithSecondsInEntryTimes = 11;
const kDbVersionWithRemoveProStatus = 12;
const kDbVersionWithEntryTimerStatus = 13;
const kDbVersionWithLocationTrackingTables = 14;
const kDbVersionWithLocationPointSpeed = 15;
const kDbVersionWithUserLocationTracking = 16;
const kDbVersionWithResourceTables = 17;
const kDbVersionWithAttachmentDoodle = 18;
const kDbVersionWithQrTables = 19;
const kDbVersionWithOrganizationHasUserSeen = 20;
const kDbVersionWithFeedTables = 21;
const kDbVersionWithPersonTables = 22;
const kDbVersionWithProjectExtraFields = 23;
const kDbVersionWithEmailAddressPhoneTables = 24;
const kDbVersionWithSyncMetadataSyncVersionColumn = 25;
const kDbVersionWithTemplatelogTables = 26;
const kDbVersionWithSignatureTable = 27;
const kDbVersionWithTemplateGroupNameNullable = 28;
const kDbVersionWithAccessRules = 29;
const kDbVersionWithEntrySource = 30;
const kDbVersionWithEntryGroupTables = 31;
const kDbVersionWithCustomFieldAndCustomFieldAllowedValueParent = 32;
const kDbVersionWithTemplateCondition = 33;
const kDbVersionWithMetadataAllowedValueId = 34;
const kDbVersionWithEntryMetadata = 35;
const kDbVersionWithTemplateRootIdAndIsActive = 36;
const kDbVersionWithFeatureFlags = 37;
const kDbVersionWithProductTables = 38;
const kDbVersionWithEntryDraftTable = 39;

const _dbFile = 'bitacora.db';
const _dbVersion = kDbVersionWithEntryDraftTable;

class DbRepository extends Repository<DbContext> {
  final SqfliteUtils _sqfliteUtils = SqfliteUtils();
  final PlatformUtils _platformUtils = PlatformUtils();
  final DbLockKey key;

  final String file;

  @override
  RepositoryCache cache = RepositoryCache();

  @override
  final AccessDbTable access = AccessDbTable();
  @override
  final AttachmentDbTable attachment = AttachmentDbTable();
  @override
  final AvatarDbTable avatar = AvatarDbTable();
  @override
  final AddressDbTable address = AddressDbTable();
  @override
  final CustomFieldDbTable customField = CustomFieldDbTable();
  @override
  final TemplateGroupDbTable templateGroup = TemplateGroupDbTable();
  @override
  final CustomFieldMetadataDbTable customFieldMetadata =
      CustomFieldMetadataDbTable();
  @override
  final CustomFieldOptionsDbTable customFieldOptions =
      CustomFieldOptionsDbTable();
  @override
  final CustomFieldAllowedValueDbTable customFieldAllowedValue =
      CustomFieldAllowedValueDbTable();
  @override
  final EntryDbTable entry = EntryDbTable();
  @override
  final EntryDraftDbTable entryDraft = EntryDraftDbTable();
  @override
  final EntryGroupDbTable entryGroup = EntryGroupDbTable();
  @override
  final EntryGroupEntryDbTable entryGroupEntry = EntryGroupEntryDbTable();
  @override
  final EntrySourceDbTable entrySource = EntrySourceDbTable();
  @override
  final EntryMetadataDbTable entryMetadata = EntryMetadataDbTable();
  @override
  final EmailDbTable email = EmailDbTable();
  @override
  final FeatureFlagDbTable featureFlag = FeatureFlagDbTable();
  @override
  final FeedPostDbTable feedPost = FeedPostDbTable();
  @override
  final InventorylogDbTable inventorylog = InventorylogDbTable();
  @override
  final LocationPointDbTable locationPoint = LocationPointDbTable();
  @override
  final LocationTrackingDbTable locationTracking = LocationTrackingDbTable();
  @override
  final OpenStateDbTable openState = OpenStateDbTable();
  @override
  final OrganizationDbTable organization = OrganizationDbTable();
  @override
  final PersonDbTable person = PersonDbTable();
  @override
  final PersonDetailDbTable personDetail = PersonDetailDbTable();
  @override
  final PersonnellogDbTable personnellog = PersonnellogDbTable();
  @override
  final PhoneDbTable phone = PhoneDbTable();
  @override
  final ProgresslogDbTable progresslog = ProgresslogDbTable();
  @override
  final ProjectDbTable project = ProjectDbTable();
  @override
  final ProductDbTable product = ProductDbTable();
  @override
  final QrCodeDbTable qrCode = QrCodeDbTable();
  @override
  final ReportDbTable report = ReportDbTable();
  @override
  final ReportTemplateDbTable reportTemplate = ReportTemplateDbTable();
  @override
  final ResourceDbTable resource = ResourceDbTable();
  @override
  final ResourceCategoryDbTable resourceCategory = ResourceCategoryDbTable();
  @override
  final SyncMetadataDbTable syncMetadata = SyncMetadataDbTable();
  @override
  final SignatureDbTable signature = SignatureDbTable();
  @override
  final TagDbTable tag = TagDbTable();
  @override
  final TemplateDbTable template = TemplateDbTable();
  @override
  final TemplateBlockDbTable templateBlock = TemplateBlockDbTable();
  @override
  final TemplateConditionDbTable templateCondition = TemplateConditionDbTable();
  @override
  final TemplatelogDbTable templatelog = TemplatelogDbTable();
  @override
  final UserDbTable user = UserDbTable();
  @override
  final UserInviteDbTable userInvite = UserInviteDbTable();
  @override
  final WorklogDbTable worklog = WorklogDbTable();
  @override
  final OutgoingMutationDbTable outgoingMutation = OutgoingMutationDbTable();

  Future<Database>? _db;
  DbLock? _lock;
  String? _path;
  bool _isNuked = false;
  Future<void>? _closing;

  factory DbRepository({
    DbLockKey key = DbLockKey.foreground,
    String file = _dbFile,
  }) =>
      inject(() => DbRepository._(key, file));

  DbRepository._(this.key, this.file);

  @override
  Future<String> get path async {
    _path ??= join(
        _platformUtils.isIOS
            ? (await getLibraryDirectory()).path
            : await _sqfliteUtils.databasesPath,
        file);
    return _path!;
  }

  DbLock get lock => _lock ??= DbLockInjector().get(key, close);

  @override
  Future<void> close() async {
    logger.i('db Closing...');
    if (_closing != null) {
      await _closing;
    }

    final completer = Completer();
    _closing = completer.future;

    try {
      cache.clear();
      final closed = _db;
      _lock?.close();
      if (closed != null) {
        _db = null;
        _lock = null;
        final db = await closed;
        await db.close();
      }
    } finally {
      completer.complete();
      _closing = null;
    }
  }

  @override
  Future<void> nuke() async {
    cache.clear();
    _isNuked = true;
    await _sqfliteUtils.deleteDatabase(await path);
    _db = null;
  }

  @override
  Future<T> transaction<T>(Future<T> Function(DbContext context) action) async {
    return (await _openDb()).transaction<T>(
      (txn) async {
        try {
          return await action(DbContext(db: this, txn: txn));
        } catch (_) {
          logger.i('db Transaction failed, clearing cache');
          cache.clear();
          rethrow;
        }
      },
    );
  }

  Future<DatabaseExecutor> executor(DbContext context) async {
    return LoggingExecutor(context.txn ?? (await _openDb()));
  }

  @override
  DbContext context({
    Fields? fields,
    Cursor? cursor,
    QueryScope? queryScope,
    Map<String, dynamic>? props,
  }) {
    return DbContext(
      db: this,
      fields: fields == null ? null : fields as DbFields,
      cursor: cursor == null ? null : cursor as LimitOffsetCursor,
      queryScope: queryScope,
      props: props,
    );
  }

  @override
  void markDirty() {
    cache.clear();
    avatar.markDirty();
    access.markDirty();
    address.markDirty();
    attachment.markDirty();
    customField.markDirty();
    customFieldMetadata.markDirty();
    customFieldOptions.markDirty();
    customFieldAllowedValue.markDirty();
    entry.markDirty();
    entryDraft.markDirty();
    entryGroup.markDirty();
    entryGroupEntry.markDirty();
    entrySource.markDirty();
    entryMetadata.markDirty();
    email.markDirty();
    featureFlag.markDirty();
    feedPost.markDirty();
    inventorylog.markDirty();
    locationPoint.markDirty();
    locationTracking.markDirty();
    openState.markDirty();
    organization.markDirty();
    person.markDirty();
    personDetail.markDirty();
    outgoingMutation.markDirty();
    personnellog.markDirty();
    phone.markDirty();
    progresslog.markDirty();
    project.markDirty();
    product.markDirty();
    qrCode.markDirty();
    report.markDirty();
    reportTemplate.markDirty();
    resource.markDirty();
    resourceCategory.markDirty();
    syncMetadata.markDirty();
    signature.markDirty();
    tag.markDirty();
    template.markDirty();
    templateBlock.markDirty();
    templateCondition.markDirty();
    templateGroup.markDirty();
    templatelog.markDirty();
    user.markDirty();
    userInvite.markDirty();
    worklog.markDirty();
  }

  Future<Database> _openDb() async {
    if (_isNuked) {
      throw 'This db session ended.';
    }

    try {
      if (_closing != null) {
        await _closing;
      }
      await lock.verify();
    } catch (_) {
      cache.clear();
      _lock = null;
      _db = null;
      rethrow;
    }

    return _db ??= _openFutureDb();
  }

  Future<Database> _openFutureDb() async {
    logger.i('db Opening...');
    return _sqfliteUtils.openDatabase(
      await path,
      version: _dbVersion,
      onCreate: (db, version) async {
        for (final contract in contracts) {
          await db.execute(contract.create);
        }
        _saveDbCreateInfo();
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        for (final contract in contracts) {
          await contract.upgradeOrCreate(db, oldVersion, newVersion);
        }
      },
    );
  }

  List<DbContract> get contracts => const [
        AccessDbContract(),
        AccessEntryDbContract(),
        AddressDbContract(),
        AvatarDbContract(),
        AttachmentDbContract(),
        CustomFieldDbContract(),
        CustomFieldMetadataDbContract(),
        CustomFieldAllowedValueDbContract(),
        CustomFieldOptionsDbContract(),
        EntryDbContract(),
        EntryDraftDbContract(),
        EntryGroupDbContract(),
        EntryGroupEntryDbContract(),
        EntrySourceDbContract(),
        EntryMetadataDbContract(),
        EmailDbContract(),
        FeatureFlagDbContract(),
        FeedPostDbContract(),
        InventorylogDbContract(),
        LocationPointDbContract(),
        LocationTrackingDbContract(),
        OpenStateDbContract(),
        OrganizationDbContract(),
        OutgoingMutationDbContract(),
        PersonDbContract(),
        PersonDetailDbContract(),
        PersonnellogDbContract(),
        PhoneDbContract(),
        ProgresslogDbContract(),
        ProjectDbContract(),
        ProjectEntriesProjectDbContract(),
        ProductDbContract(),
        ProductUserDbContract(),
        QrCodeDbContract(),
        ReportDbContract(),
        ReportTemplateDbContract(),
        ResourceDbContract(),
        ResourceAggregationDbContract(),
        ResourceCategoryDbContract(),
        ResourceCategoryResourceDbContract(),
        SignatureDbContract(),
        SyncMetadataDbContract(),
        TagDbContract(),
        TagEntryDbContract(),
        TemplateDbContract(),
        TemplateBlockDbContract(),
        TemplateConditionDbContract(),
        TemplateGroupDbContract(),
        TemplatelogDbContract(),
        UserDbContract(),
        UserInviteDbContract(),
        UserLocationTrackingDbContract(),
        UserOrganizationDbContract(),
        WorklogDbContract(),
      ];

  @override
  Future<U> query<U>(
    RepositoryQuery<U> repositoryQuery, {
    DbContext? context,
  }) {
    return repositoryQuery.query(context ?? this.context());
  }

  void _saveDbCreateInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        SharedPreferencesKeys.dbCreateAppVersion, packageInfo.version);
    await prefs.setInt(SharedPreferencesKeys.dbCreateDateTime,
        Clock().now().millisecondsSinceEpoch);
  }
}
