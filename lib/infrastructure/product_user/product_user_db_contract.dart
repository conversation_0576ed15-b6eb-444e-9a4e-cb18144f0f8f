import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class ProductUserDbContract extends DbContract {
  static const String _ = 'pu_';
  static const String _tableName = 'productUser';

  final String productId = '${_}productId';
  final String userId = '${_}userId';

  const ProductUserDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithProductTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $productId INTEGER NOT NULL,
    $userId INTEGER NOT NULL,
    PRIMARY KEY ($productId, $userId)
  )
  ''';
}
