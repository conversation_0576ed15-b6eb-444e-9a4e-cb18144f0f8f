import 'package:bitacora/domain/entry/entry.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class EntryFormPageMetadataNotification extends StatefulWidget {
  final Entry entry;

  const EntryFormPageMetadataNotification({
    super.key,
    required this.entry,
  });

  @override
  State<EntryFormPageMetadataNotification> createState() =>
      _TranscriptNotificationState();
}

class _TranscriptNotificationState
    extends State<EntryFormPageMetadataNotification> {
  bool _showTranscript = false;

  String? get _transcription {
    if (widget.entry.source?.metadata?.map != null &&
        widget.entry.source!.metadata!.map!['transcription'] != null &&
        widget.entry.source!.metadata!.map!['transcription'].isNotEmpty) {
      return widget.entry.source?.metadata?.map?['transcription'];
    }
    return null;
  }

  Widget _buildSentimentChip(String sentiment) {
    final Map<String, Color> sentimentColors = {
      'Happiness': Colors.green,
      'Sadness': Colors.blue,
      'Anger': Colors.red,
      'Surprise': Colors.purple,
      'Calmness': Colors.teal,
      'Frustration': Colors.orange,
    };

    final color = sentimentColors[sentiment] ?? Colors.grey;

    return Chip(
      label: Text(sentiment),
      backgroundColor: color.withAlpha(50),
      side: BorderSide(color: color.withAlpha(125)),
      labelStyle: TextStyle(color: color),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasDataToShow) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final textColor = theme.textTheme.bodyMedium?.color ?? Colors.black;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: double.infinity,
      decoration: BoxDecoration(
        color: primaryColor.withAlpha(20),
        border: Border.all(color: primaryColor.withAlpha(180)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _showTranscript = !_showTranscript;
              });
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              child: Row(
                children: [
                  Icon(Icons.auto_awesome, color: primaryColor),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)!.aiGeneratedNotification,
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(
                    _showTranscript
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: primaryColor,
                  ),
                ],
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: const SizedBox(height: 0),
            secondChild: Container(
              width: double.infinity,
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: 16,
              ),
              decoration: BoxDecoration(
                color: primaryColor.withAlpha(20),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Divider(color: primaryColor),
                  const SizedBox(height: 8),
                  if (_transcription != null) ...[
                    Text(
                      AppLocalizations.of(context)!.transcript,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _transcription!,
                      style: TextStyle(
                        color: textColor,
                      ),
                    ),
                  ],
                  if (widget.entry.metadataSentiment != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.sentiment,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildSentimentChip(
                        widget.entry.metadataSentiment!.value!.displayValue),
                  ],
                  if (widget.entry.metadataKeywords.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.keywords,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: widget.entry.metadataKeywords
                          .map((keyword) => Chip(
                                label: Text(keyword.value!.displayValue),
                                backgroundColor: theme.colorScheme.surface,
                                labelStyle: TextStyle(fontSize: 12),
                              ))
                          .toList(),
                    ),
                  ],
                  if (widget.entry.metadataActionItems.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.actionItems,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...widget.entry.metadataActionItems.map(
                      (action) => Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.check_circle_outline,
                                size: 16, color: primaryColor),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                action.value!.displayValue,
                                style: TextStyle(color: textColor),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                  if (widget.entry.metadataHealthItems.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Health Recommendations',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...widget.entry.metadataHealthItems.map(
                      (item) => Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.favorite_outline,
                                size: 16, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                item.value!.displayValue,
                                style: TextStyle(color: textColor),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                  if (widget.entry.metadataHealthTags.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Health Tags',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: widget.entry.metadataHealthTags
                          .map((tag) => Chip(
                                label: Text(tag.value!.displayValue),
                                backgroundColor: Colors.red.withAlpha(25),
                                side:
                                    BorderSide(color: Colors.red.withAlpha(76)),
                                labelStyle: TextStyle(
                                    fontSize: 12, color: Colors.red.shade700),
                                avatar: Icon(Icons.favorite,
                                    size: 12, color: Colors.red.shade700),
                              ))
                          .toList(),
                    ),
                  ],
                ],
              ),
            ),
            crossFadeState: _showTranscript
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  bool get _hasDataToShow {
    return _transcription != null ||
        widget.entry.metadataSentiment?.value != null ||
        widget.entry.metadataKeywords.isNotEmpty ||
        widget.entry.metadataActionItems.isNotEmpty ||
        widget.entry.metadataHealthItems.isNotEmpty ||
        widget.entry.metadataHealthTags.isNotEmpty;
  }
}
