import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_floating_action_button.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_floating_action_button_animation_controller.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder_controller.dart';
import 'package:flutter/material.dart';

class DaylogAiOptionController {
  final ValueNotifier<ProcessingState> processingState =
      ValueNotifier(ProcessingState.idle);
  late final DaylogAiFloatingActionButtonAnimationController
      animationController;
  final ValueNotifier<bool> isExpanded = ValueNotifier(false);
  final ValueNotifier<SimpleAudioRecorderController?>
      audioRecorderControllerNotifier = ValueNotifier(null);

  void setup(TickerProvider vsync) {
    animationController =
        DaylogAiFloatingActionButtonAnimationController(vsync, isExpanded);
  }

  void dispose() {
    animationController.dispose();
    processingState.dispose();
    audioRecorderControllerNotifier.value?.dispose();
  }

  void toggleAnimation() {
    animationController.toggle();
  }

  void _startProcessing() {
    animationController.collapse();
    processingState.value = ProcessingState.processing;
    isExpanded.value = false;
  }

  void _completeProcessing() {
    processingState.value = ProcessingState.completed;
    processingState.value = ProcessingState.idle;
  }

  void handleAudioRecordingCancel() async {
    await audioRecorderControllerNotifier.value?.cancelRecording();
    audioRecorderControllerNotifier.value = null;
  }

  void handleAudioRecordingSave() async {
    await audioRecorderControllerNotifier.value!.stopRecording();
  }

  void handleGeneration(
    BuildContext context,
    DaylogAiButtonSource source,
  ) async {
    final contextSnapshot = DaylogAiButtonSourceRunnerContextSnapshot(context);

    animationController.collapse();
    _startProcessing();

    if (source == DaylogAiButtonSource.audio) {
      audioRecorderControllerNotifier.value = SimpleAudioRecorderController();
    }

    final runner = DaylogAiButtonSourceGenerator.build(
      contextSnapshot,
      source,
      audioRecorderControllerNotifier,
    );
    final entry = await runner.run();

    _completeProcessing();

    if (entry == null) {
      return;
    }

    final db = contextSnapshot.read<Repository>();

    await db.entryDraft.save(db.context(), EntryDraft(entry: entry));
  }
}
