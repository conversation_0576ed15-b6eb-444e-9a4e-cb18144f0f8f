import 'package:flutter/material.dart';

const _kAiAnimationDuration = 300;

class DaylogAiFloatingActionButtonAnimationController {
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  final ValueNotifier<bool> isExpanded;

  DaylogAiFloatingActionButtonAnimationController(
    TickerProvider vsync,
    this.isExpanded,
  ) {
    _animationController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: _kAiAnimationDuration),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  Animation<double> get animation => _animation;

  void collapse() {
    _animationController.reverse();
  }

  void toggle() {
    isExpanded.value = !isExpanded.value;
    if (isExpanded.value) {
      _animationController.forward();
      isExpanded.value = true;
    } else {
      _animationController.reverse();
      isExpanded.value = false;
    }
  }

  void dispose() {
    _animationController.dispose();
  }
}
